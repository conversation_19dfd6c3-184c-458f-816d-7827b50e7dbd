
import DropDownMultipleDatatableHeader from "../components/utility/dropdownmultipledatatableheader.js"
import UtilityLib from "../components/utility/utilitylib.js";
import { CustomDivIcon } from '../components/custom-div-icon.js';
import NewMapModel from "../components/newmapmodel.js";
import shareMapDialog from "../../../core/js/components/sitefotos/share-map-dialog.js";

export default {
    template: /*html*/`<v-card>
    <v-toolbar dense id="map-launcher" v-if="mapState=='editing'" class="elevation-1" :width="mapState!='editing' ? '35vw' : '35vw'" max-width="300px" style="border-radius:0px!important;">
    <template >
        <v-text-field outlined dense   label="Map Name" hide-details   v-model="fileName" single-line>
        <template v-slot:prepend-inner>
              <v-icon>mdi-map-marker</v-icon>
            </template>

        </v-text-field>
    </template>


</v-toolbar>


  <new-map-model :buildings="buildings" :new-map-model="newMapModel" @newblankmap="newBlankMap" @newsitemap="newSiteMap" :cancelable="cancelable" @canceled="canceled"></new-map-model>



  <v-navigation-drawer v-if="mapState=='editing'" hide-overlay stateless v-model="groupPanel" right id="group-panel" style='width:302px'>
  <template v-if="Math.max(...Object.entries(globalMapManagerComputed.presets).map(a => Object.keys(a[1].leafletlayer._layers).length)) > 0">
    <!--div id="notch-icon" @click="groupPanel=!groupPanel" ><template v-if="groupPanel"><i style="color:#ffffff;" class="fa fa-caret-right"></i></template><template v-else><i style="color:#ffffff;" class="fa fa-caret-left"></i></template></div></template-->
    <v-expansion-panels id="map-sidebar" accordion>
      <template v-for="(preset,i) in presets">
        <template v-if="preset.leafletlayer._layers != 'undefined'">
          <template v-if="Object.keys(preset.leafletlayer._layers).length > 0">
            <v-expansion-panel :style="getGradientStyle(preset)" @click="selectGroup(preset.shortname)" :key="preset.leafletlayer._leaflet_id" class="elevation-0">
              <v-expansion-panel-header hide-actions class="pt-0 pb-0" disable-icon-rotate>
                <v-row no-gutters style="width: 100%; height:42px;">
                  <v-col cols="5" style="align-self: center">
                    <span style="font-size:1rem; font-weight:600;width: 100%; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;display: inline-block;" :title="preset.name">
                      {{preset.name}}
                    </span>
                  </v-col>
                  <v-col cols="2" style="align-self: center"><v-badge  color="primary" :content="(Object.keys(preset.leafletlayer._layers).length)" style="font-size:1rem; font-weight:600;"></v-badge></v-col>
                  <v-col style="align-self: center;font-size:0.6rem; font-weight:500;" cols="3">{{preset.grouparea}}</v-col>
                  <v-col cols="2" style="align-self: center">
                    <v-btn icon @click.stop="toggleGroup(preset.shortname)" title="Toggle Layer">
                      <v-icon style="text-align: end" color="blue darken-2" v-if="preset.leafletlayer.metadata.groupVisible">mdi-eye </v-icon>
                      <v-icon style="text-align: end" v-else>mdi-eye-off</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-expansion-panel-header>
              <v-expansion-panel-content>
              <v-row no-gutters style="width: 100%" class="caption">
                                    <v-col cols="6" style="font-size:0.6rem; font-weight:500;">{{preset.grouparea}}</v-col>
                                    <v-col cols="4" style="font-size:0.6rem; font-weight:500;">{{preset.groupperim}}</v-col>
                                    <v-col cols="2">
                                        <v-btn icon small @click.stop="toggleLegend(preset.shortname)" title="Toggle Legend">
                                            <v-icon v-if="preset.leafletlayer.metadata.legendVisible" color="green darken-2" >
                                                mapbuilder-legend-key-on
                                            </v-icon>
                                            <v-icon v-else >mapbuilder-legend-key-off</v-icon>
                                        </v-btn>

                                    </v-col>
                                </v-row>
               
                <template v-for="(layer, j) in preset.leafletlayer._layers">
                  <v-row no-gutters style="width: 100%" @click="selectLayer(layer)" class="caption" :key="layer._leaflet_id">
                    <v-col cols="1">
                      <template v-if="layer.feature.properties.type == 'Polygon'">
                        <v-icon small :color="preset.fillColor">mapbuilder-polygon</v-icon>
                      </template>
                      <template v-if="layer.feature.properties.type == 'Rectangle'">
                        <v-icon small :color="preset.fillColor">fa-square</v-icon>
                      </template>
                      <template v-if="layer.feature.properties.type == 'Circle'">
                        <v-icon small :color="preset.fillColor">fa-circle</v-icon>
                      </template>
                      <template v-if="layer.feature.properties.type == 'Line'">
                        <v-icon small :color="preset.color">fa-arrows-h</v-icon>
                      </template>
                      <template v-if="layer.feature.properties.type == 'Photo' || layer.feature.properties.type == 'Marker'">
                        <v-img :src="layer.feature.properties.markerURL" width="12"></v-img>
                      </template>
                    </v-col>
                    <v-col cols="5">{{layer.feature.properties.name ?
                                           layer.feature.properties.name.substring(0, 9) :
                                           layer.feature.properties.type +
                                           layer.feature.properties.number}}
                    </v-col>
                    <v-col cols="6" style="text-align: end; font-size:0.6rem; font-weight:500;">
                      <template v-if="layer.feature.properties.type == 'Line'">{{layer.feature.properties.perimeter}}</template>
                      <template v-else>{{layer.feature.properties.area}}</template>
                    </v-col>
                  </v-row>
                </template>
              </v-expansion-panel-content>
            </v-expansion-panel>
          </template>
        </template>
      </template>
    </v-expansion-panels>
  </template>
</v-navigation-drawer>

    <v-dialog overlay-opacity="0" id="default-grp-dialog" v-if="defaultGroupDialog" v-model="defaultGroupDialog"
              persistent
              max-width="400">
        <v-card>
            <v-card-title>Choose Layer</v-card-title>
            <v-card-text>
                <v-autocomplete outlined hide-details dense placeholder="Choose Layer for Item" v-model="defaultGroupDialogInput"
                                :items="groupItems" item-value="value"
                                item-text="text"></v-autocomplete>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn color="primary" @click="selectDefaultGroup">Submit</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
    <v-dialog overlay-opacity="0" id="assign-site-dialog" v-if="assignSiteDialog" v-model="assignSiteDialog"
    persistent
    max-width="400">
<v-card>

  <v-card-title>Assign map to a site</v-card-title>
  <v-card-text>

  
      <v-autocomplete outlined hide-details dense placeholder="Choose Site" v-model="buildingID"
                      :items="buildings" item-value="mb_id"
                      item-text="mb_nickname"></v-autocomplete>
  </v-card-text>
  <v-card-actions>
      <v-spacer></v-spacer>
      <v-btn v-if="!saveAsLoading" @click="cancelAssign" text>Cancel</v-btn> 
      <v-btn :loading="saveAsLoading" color="primary" @click="assignSite">Submit</v-btn>
  </v-card-actions>
</v-card>
</v-dialog>
    <v-dialog persistent overlay-opacity="0" id="save-as-dialog" v-if="saveAsDialog" v-model="saveAsDialog"
              max-width="400">
        <v-card>
            <v-toolbar class="elevation-0 white">
                <v-toolbar-title>
                    <h3 class="headline mb-0">Save Map</h3>
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon @click="saveAsDialog=false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text>
                <v-autocomplete outlined dense hide-details v-if="buildings.length>0" placeholder="Select a site" v-model="buildingID"
                                :items="buildings" item-text="mb_nickname"
                                item-value="mb_id"></v-autocomplete>
                <v-text-field outlined class='pt-2' dense hide-details v-model="fileName" placeholder="Map Name"></v-text-field>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn :loading="saveAsLoading" color="primary" @click="saveAs">Go</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
    <v-dialog
      max-width="600px"
      overlay-opacity="0"
      :hide-overlay="$vuetify.breakpoint.smAndDown"
      transition="dialog-bottom-transition"
      :fullscreen="$vuetify.breakpoint.smAndDown"
      v-if="shareMapDialog"
      v-model="shareMapDialog"
      persistent
    >
        <share-map-dialog
          :map-link-url="globalBaseURLComputed + '/vpics/guestmap?' + urlKey"
          :shareByEmailLoading="shareByEmailLoading"
          :shareByPhoneLoading="shareByPhoneLoading"
          :shareEmail="shareEmail"
          :sharePhone="sharePhone"
          @update:dialog="shareMapDialog = false"
          @click:shareByEmail="shareByEmail"
          @click:shareByPhone="shareByPhone"
        />
<!--<v-card>-->
<!--  <v-toolbar  class="elevation-0 white">-->
<!--      <v-toolbar-title>Share Map-->
<!--      </v-toolbar-title>-->
<!--      <v-spacer></v-spacer>-->
<!--      <v-btn icon @click="shareMapDialog = false">-->
<!--          <v-icon>close</v-icon>-->
<!--      </v-btn>-->
<!--  </v-toolbar>-->
<!--  <v-card-text>-->
<!-- <v-row>-->
<!--<v-col cols="8"> <v-text-field outlined  clearable label="Enter the email address of the recipient" dense hide-details-->
<!-- v-model="shareEmail"></v-text-field></v-col><v-col><v-btn :disabled="shareEmail.length == 0" outlined class="float-right" @click="shareByEmail" color="primary" style="text-transform: none !important;">Send Email</v-btn></v-col>-->
<!-- </v-row>-->
<!-- <v-row>-->
<!--<v-col cols="8"> <v-text-field outlined  clearable label="Enter the phone number of the recipient" dense hide-details-->
<!-- v-model="sharePhone"></v-text-field></v-col><v-col><v-btn :disabled="sharePhone.length == 0" outlined class="float-right" @click="shareByPhone" color="primary" style="text-transform: none !important;">Send Text</v-btn></v-col>-->
<!-- </v-row>-->
<!-- <v-row>-->
<!--<v-col>Link</v-col>-->
<!-- </v-row>-->
<!-- <v-row>-->
<!-- <v-col>-->
<!--<a target="_blank" :href="globalBaseURLComputed + '/vpics/guestmap?' + urlKey">{{globalBaseURLComputed + '/vpics/guestmap?' + urlKey}}</a></v-col>-->
<!-- </v-row>-->

<!--  </v-card-text>-->
<!--</v-card>-->

</v-dialog>
    <v-dialog overlay-opacity="0" :hide-overlay="$vuetify.breakpoint.smAndDown" width="60vw"
              transition="dialog-bottom-transition" :fullscreen="$vuetify.breakpoint.smAndDown"
              v-if="mergemapDialog" v-model="mergemapDialog" persistent
    >
        <v-card>
            <v-toolbar  class="elevation-0 white">
                <v-toolbar-title>Merge Map
                </v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn icon @click="mergemapDialog = false">
                    <v-icon>close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text>
            <v-toolbar flat class="pt-0 transparent">

                            <v-spacer></v-spacer>
                            <v-text-field class="shrink" outlined clearable label="Search Maps" dense hide-details
                                          append-icon="search" v-model="search"></v-text-field>


             </v-toolbar>
              <v-data-table height="25vw" @click:row="mergeMap" id="mergemap-dialog-table"
                                      item-key="LayerID"
                                       fixed-header :single-expand="true"
                                      :search="search" :items-per-page="10"
                                      :footer-props="{itemsPerPageOptions: [10,20,50,{'text':'All','value':-1}]}"
                                      :headers="headers" :items="filteredFiles" :options="pagination">
                            <template v-slot:header.sitename="{ header }">
                                <drop-down-multiple-datatable-header v-if="header.value=='sitename'"
                                                                     :title="header.text"
                                                                     :items="uniqueSites" item-text="sitename"
                                                                     item-value="sitename"
                                                                     v-model="filter.sites"></drop-down-multiple-datatable-header>
                            </template>
                            </v-data-table>

            </v-card-text>
            <v-card-actions><v-spacer></v-spacer><v-btn @click="mergemapDialog = false" text>Cancel</v-btn></v-card-actions>
        </v-card>
    </v-dialog>
    <v-snackbar v-model="snackbar.snackbar" :bottom="snackbar.y === 'bottom'" :left="snackbar.x === 'left'"
                :right="snackbar.x === 'right'" :timeout="snackbar.timeout" :top="snackbar.y === 'top'">{{
        snackbar.text }}
        <v-btn color="primary" @click="snackbar.snackbar = false">Close</v-btn>
    </v-snackbar>
    <UtilityLib ref="utilityLibManager"></UtilityLib>
    <v-dialog v-model="saveLoader" width="300" persistent>
        <v-card>
            <v-card-title>
                Saving File
            </v-card-title>
            <v-card-text class="my-2" v-if="!saveCompleted">
                <div class="text-center">
                    <v-progress-circular
                      :size="50"
                      :width="7"
                      indeterminate
                      color="primary"
                    ></v-progress-circular>
                    <p>Saving file...</p>
                </div>
            </v-card-text>
            <v-card-text class="my-2" v-else>
                <div class="text-center mt-5">
                    <p>File saved successfully</p>
                </div>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn v-if="saveCompleted" color="primary" @click="saveLoader=false">OK</v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</v-card>`,
    watch: {
        mapState(val) {
            if (val == "newmap") {

                this.maptype = "address"
                store.commit('map/clearData')
                store.commit('baselayer/clearDataAll')
                this.clearScene(false);
                this.cancelable = false;
                this.resetMap();
                this.newMapModel = true;
            }
        }
    },
    components: { DropDownMultipleDatatableHeader, UtilityLib, NewMapModel, shareMapDialog },
    props: ['unsetHighlightLayer', 'setHighlightLayer', 'onPolyClick', 'fitBounds'],

    mounted() {
        this.initializePresets()

    },
    created() {
        this.animations = [];
        this.arrowHeads = [];
        this.presets = {};
        this.groups = [];
        this.updateData();
        window.globalMapManager = this;
    },
    destroyed() {
        window.globalMapManager = null;
        delete window.globalMapManager;
    },


    data: function () {
        return {
            saveAsLoading: false,
            dvalue: [],
            saveLoader: false,
            saveCompleted: false,
            buildings: [],
            cancelable: false,
            files: [],
            search: "",
            assignSiteDialog: false,
            sharePhone: "",
            shareEmail: "",
            shareByPhoneLoading: false,
            shareByEmailLoading: false,
            newMapModel: false,
            newMapMode: true,
            filter: {
                sites: [],
            },
            headers: [{
                text: 'Map Name',
                align: 'left',
                value: 'LayerName',
                width: '30%',
                class: 'tableheader'
            }, {
                text: 'Site',
                align: 'left',
                value: 'sitename',
                width: '30%',
                class: 'tableheader'
            },
            {
                text: 'Date Modified',
                align: 'left',
                value: 'modified',
                width: '20%',
                class: 'tableheader'
            },

            ],
            pagination: {
                rowsPerPage: 50,
                sortBy: ["modified"],
                sortDesc: [true]
            },
            addressdata: null,
            dotMenu1: false,
            tempBid: 0,
            dotMenu2: false,
            defaultGroupDialog: false,
            defaultGroupDialogInput: null,
            defaultGroupDialogResolve: null,
            defaultGroupDialogReject: null,
            groupPanel: true,
            saveAsDialog: false,
            snackbar: {
                snackbar: false,
                y: 'bottom',
                x: 'left',
                mode: '',
                timeout: 2000,
                text: ''
            },
        };
    },
    methods: {
        shownewMapModel(cancelable) {
            if (cancelable)
                this.cancelable = true;
            this.newMapModel = true;
        },
        canceled() {
            this.newMapModel = false;
        },
        hidenewMapModel() {
            this.newMapModel = false;
        },
        mergeMap(row) {
            this.$refs.utilityLibManager.open("Confirm", "Do you want to merge the map?", "Yes", "No", true).then(async function (result) {
                if (result) {
                    const formBody = new URLSearchParams();
                    formBody.append("accessCode", store.get('map/accessCode'));
                    formBody.append("LayerID", row.LayerID);
                    formBody.append("debugData", "mapmanager.js => mergemap");
                    let response = await fetch(myBaseURL + '/node/maps/open-map-layer', {
                        method: 'POST',
                        body: formBody,
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    });

                    if (response.ok) {
                        let data = await response.text()
                        if (data.slice(-6).indexOf('}') == -1) {
                            var result = JSON.parse(data.substring(0, data.length -
                                6));
                        } else {
                            var result = JSON.parse(data);
                        }

                        if (result === Object(result)) {
                            this.importv1Format(result, false);
                        } else {
                            result = JSON.parse(result);
                            this.openv2format(result, false);
                        }
                    }
                }
            }.bind(this))
        },
        selectLayer(layer) {

            let groupId = layer.feature.properties?.preset;
            if (groupId) {
                if (this.presets[groupId].groupVisible) {

                    layer.fireEvent('click');
                } else {
                    this.snackbar.text = 'The item is currently invisible please make it visible and try again.'
                    this.snackbar.snackbar = true;

                }
            }
            else
                layer.fireEvent('click');
        },
        async shareByPhone(cell) {
            this.shareByPhoneLoading = true;
           
            const url_key = this.urlKey;
            let urllink = "";
            if (url_key.length > 2)
                urllink = myBaseURL + '/vpics/guestmap?' + url_key;
            const formData = new FormData();
            formData.append('vid', store.get('map/accessCode'));
            formData.append('cell', cell);
            formData.append('filename', this.fileName);
            formData.append('companyname', this.cName);
            formData.append('url', urllink);
            formData.append('bid', this.buildingID);
            const req = await fetch(myBaseURL + '/vpics/sendtextsharemap', { method: 'POST', body: formData });
            const res = await req.text();
            this.sharePhone = "";
            this.shareByPhoneLoading = false;
            this.snackbar.text = 'Map has been shared by text'
            this.snackbar.snackbar = true;
            this.shareMapDialog = false;
        },
        async shareByEmail(email_address) {
            this.shareByEmailLoading = true;
           
            const url_key = this.urlKey;
            let urllink = "";
            if (url_key.length > 2)
                urllink = myBaseURL + '/vpics/guestmap?' + url_key;
            const formData = new FormData();
            formData.append('vid', store.get('map/accessCode'));
            formData.append('email_address', email_address);
            formData.append('filename', this.fileName);
            formData.append('companyname', this.cName);
            formData.append('url', urllink);
            formData.append('bid', this.buildingID);
            const req = await fetch(myBaseURL + '/vpics/emailsharemap', { method: 'POST', body: formData });
            const res = await req.text();
            this.shareEmail = "";
            this.shareByEmailLoading = false;
            this.snackbar.text = 'Map has been shared by email'
            this.snackbar.snackbar = true;
            this.shareMapDialog = false;


        },
        openFileManager() {
            this.dotMenu1 = false;
            this.$router.push({ name: 'fileman', params: { 'back': true } })
        },
        cancelAssign() {
            this.buildingID = this.tempBid;
            this.tempBid = 0;
            this.assignSiteDialog = false;
        },
        showAssignSiteDialog() {
            this.tempBid = this.buildingID;
            this.assignSiteDialog = true;
        },
        hideAssignSiteDialog() {
            this.assignSiteDialog = false;
        },

        assignSite: async function () {
            this.dotMenu1 = false;
            if (store.get('map/fileID') == 0) {
                this.snackbar.text = 'Please save the map first'
                this.snackbar.snackbar = true;
                return;
            }
            this.saveAsLoading = true;
            const formData = new FormData();
            formData.append('accessCode', store.get('map/accessCode'));
            formData.append('layerid', store.get('map/fileID'));
            formData.append('bid', this.buildingID);
            const req = await fetch(myBaseURL + '/vpics/assignmap', { method: 'POST', body: formData });
            const res = await req.text();
            if (res == '1') {


                this.snackbar.text = 'Map successfully moved'
                this.snackbar.snackbar = true;
                this.assignSiteDialog = false;

            } else {
                this.snackbar.text = 'Map move unsuccessful, please try again.';
                this.snackbar.snackbar = true;
                this.buildingID = this.tempBid

            }
            this.saveAsLoading = false;
        },
        getParcel: async function (lat, lon) {
            const response = await fetch(`https://parcels.sitefotos.com/1/getdata?lat=${lat}&lon=${lon}`, {
                method: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + mytoken
                }
            });
            if (response.status == 200) {
                const data = await response.text();
                return data;
            }
            else {
                return "0"
            }

        },
        resetMap() {
            var maxBounds = L.latLngBounds(
                L.latLng(24.0, -128.23), //Southwest
                L.latLng(50.09, -59.14) //Northeast
            );
            map.fitBounds(maxBounds);
            map.setZoom(4);
        },
        trySave() {
            if (this.saveAsRequired)
                this.saveAsDialog = true;
            else
                this.save();
        },
        trySaveAs() {
            this.saveAsDialog = true;
        },
        newSiteMap: async function (e) {
            //check router and see if it is set to map
            store.commit('map/clearData')
            store.commit('baselayer/clearDataAll')
            this.clearScene(false);
            this.resetMap();
            if (this.$route.name != 'map') {
                this.$router.push({ name: 'map' })
            }
            await new Promise(resolve => setTimeout(resolve, 500));
            this.newMapModel = false;

            // Refresh quota information for new map
            if (window.globalBaseLayerControl && window.globalBaseLayerControl.refreshQuota) {
                await window.globalBaseLayerControl.refreshQuota();
            }

            const mybid = e;

            let f = _.find(this.buildings, function (o) {
                return o.mb_id == mybid
            });
            let lat = f.mb_lat;
            let lng = f.mb_long;
            let parcel = await this.getParcel(lat, lng)
            store.commit('map/clearData')


            let latlng = L.latLng(lat, lng);
            let bldgicon = LredIcon;
            let marker = L.marker(latlng, {
                icon: bldgicon
            }).addTo(drawnItems);
            if (typeof parcel != 'undefined') {
                if (parcel != 0) {

                    var parsed = JSON.parse(parcel);
                    if (parsed.type == "MultiPolygon")
                        var latlngs = L.GeoJSON.coordsToLatLngs(parsed
                            .coordinates[
                            0], 1)
                    else
                        var latlngs = L.GeoJSON.coordsToLatLngs(parsed
                            .coordinates,
                            1)

                    var polygon = L.polygon(latlngs);
                    polygon.feature = polygon.feature || {};
                    polygon.feature.type = "Feature";
                    polygon.feature.properties = polygon.feature.properties || {};

                    var distance = 0;
                    latlngs = latlngs[0];
                    var area = Math.round(L.GeometryUtil.geodesicArea(latlngs) *
                        10.76391041671);


                    area = numeral(area).format('0,0');
                    polygon.feature.properties.area = area + ' sqft';
                    if (latlngs.length > 2) {
                        for (let i = 0; i < latlngs.length - 1; i++) {
                            distance += latlngs[i].distanceTo(latlngs[i + 1]);
                        }
                        distance += latlngs[latlngs.length - 1].distanceTo(
                            latlngs[
                            0]);
                        distance = distance * 3.28084;
                        distance = numeral(_round(distance, 2)).format('0,0');
                    }
                    polygon.feature.properties.perimeter = distance + ' ft';
                    if (this.presetExists('parcel')) {
                        polygon.setStyle(this.presets['parcel'].leafletlayer
                            .metadata);
                        polygon.feature.properties.type = 'Polygon';
                        polygon.feature.properties.preset = 'parcel';
                        polygon.feature.properties.name = "Polygon-" +
                            this.shapeCounter++;
                        polygon.feature.properties.color = polygon.options
                            .color;
                        polygon.feature.properties.fillColor = polygon.options
                            .fillColor;
                        polygon.feature.properties.fillOpacity = polygon
                            .options
                            .fillOpacity;
                        polygon.feature.properties.stroke = polygon.options
                            .stroke;
                        polygon.feature.properties.strokeWeight = polygon
                            .options
                            .weight;
                        this.addLayerPreset('parcel', polygon)
                        polygon.on('mouseover', function (e) {
                            globalVueMapInstance.setHighlightLayer(polygon);
                        });
                        polygon.on('mouseout', function (e) {
                            globalVueMapInstance.unsetHighlightLayer(polygon);
                        });
                        polygon.on('click', globalVueMapInstance.onPolyClick);
                        this.selectGroup("none");
                        this.fitBounds()


                    } else {
                        await this.addPreset('Parcel', 2, "#ffff00", 3, "fixed",
                            "#ffff00",
                            "0.0");
                        polygon.setStyle(this.presets['parcel'].leafletlayer
                            .metadata);
                        polygon.feature.properties.type = 'Polygon';
                        polygon.feature.properties.preset = 'parcel';
                        polygon.feature.properties.name = "Polygon-" +
                            this.shapeCounter++;
                        polygon.feature.properties.color = polygon.options
                            .color;
                        polygon.feature.properties.fillColor = polygon.options
                            .fillColor;
                        polygon.feature.properties.fillOpacity = polygon
                            .options
                            .fillOpacity;
                        polygon.feature.properties.stroke = polygon.options
                            .stroke;
                        polygon.feature.properties.strokeWeight = polygon
                            .options
                            .weight;
                        this.addLayerPreset('parcel', polygon)
                        polygon.on('mouseover', function (e) {
                            globalVueMapInstance.setHighlightLayer(polygon);
                        });
                        polygon.on('mouseout', function (e) {
                            globalVueMapInstance.unsetHighlightLayer(polygon);
                        });
                        polygon.on('click', globalVueMapInstance.onPolyClick);
                        this.selectGroup("none");
                        this.fitBounds()

                    }
                    this.fitBounds()
                    this.mapState = "editing";
                    this.tempBid = null;


                } else {
                    this.fitBounds()
                    this.mapState = "editing";
                    this.tempBid = null
                }
                store.set('map/city', f.cityname)
                store.set('map/fileName', f.mb_nickname)
                store.set('map/address', f.mb_address1)
                store.set('map/state', f.statename)
                store.set('map/buildingID', f.mb_id)
                store.set('map/zip', f.mb_zip_code)

                store.set('map/saveAsRequired', true)


            } else {

                store.set('map/city', f.cityname)
                store.set('map/fileName', f.mb_nickname)
                store.set('map/address', f.mb_address1)
                store.set('map/state', f.statename)
                store.set('map/buildingID', f.mb_id)
                store.set('map/zip', f.mb_zip_code)

                store.set('map/saveAsRequired', true)

                this.fitBounds()
                this.mapState = "editing";
                this.tempBid = null
            }


        },
        newBlankMap: async function (e) {
            const addressData = e;


            if (addressData) {
                store.commit('map/clearData')
                store.commit('baselayer/clearDataAll')
                this.clearScene(false);
                this.resetMap();
                if (this.$route.name != 'map') {
                    this.$router.push({ name: 'map' })
                }
                //wait for 200ms to make sure the map is loaded
                await new Promise(resolve => setTimeout(resolve, 500));

                // Refresh quota information for new map
                if (window.globalBaseLayerControl && window.globalBaseLayerControl.refreshQuota) {
                    await window.globalBaseLayerControl.refreshQuota();
                }
                var lat = addressData.latitude;
                var lng = addressData.longitude;
                let parcel = await this.getParcel(lat, lng)
                store.set('map/fileName', addressData.name)
                store.set('map/address', addressData.name)
                var latlng = L.latLng(lat, lng);
                var bldgicon = LredIcon;
                var marker = L.marker(latlng, {
                    icon: bldgicon
                }).addTo(drawnItems);
                if (typeof parcel != 'undefined') {
                    if (parcel != 0) {

                        var parsed = JSON.parse(parcel);
                        if (parsed.type == "MultiPolygon")
                            var latlngs = L.GeoJSON.coordsToLatLngs(parsed
                                .coordinates[
                                0], 1)
                        else
                            var latlngs = L.GeoJSON.coordsToLatLngs(parsed
                                .coordinates,
                                1)

                        var polygon = L.polygon(latlngs);
                        polygon.feature = polygon.feature || {};
                        polygon.feature.type = "Feature";
                        polygon.feature.properties = polygon.feature.properties || {};

                        var distance = 0;
                        latlngs = latlngs[0];
                        var area = Math.round(turf.area(polygon.toGeoJSON(15)) *
                            10.76391041671)


                        area = numeral(area).format('0,0');
                        polygon.feature.properties.area = area + ' sqft';
                        if (latlngs.length > 2) {
                            for (let i = 0; i < latlngs.length - 1; i++) {
                                distance += latlngs[i].distanceTo(latlngs[i + 1]);
                            }
                            distance += latlngs[latlngs.length - 1].distanceTo(
                                latlngs[
                                0]);
                            distance = distance * 3.28084;
                            distance = numeral(_round(distance, 2)).format('0,0');
                        }
                        polygon.feature.properties.perimeter = distance + ' ft';

                        if (this.presetExists('parcel')) {
                            let preset = this.getPreset('parcel');
                            polygon.setStyle(preset.leafletlayer
                                .metadata);
                            polygon.feature.properties.type = 'Polygon';
                            polygon.feature.properties.preset = 'parcel';
                            polygon.feature.properties.name = "Polygon-" +
                                this.shapeCounter++;
                            polygon.feature.properties.color = polygon.options
                                .color;
                            polygon.feature.properties.fillColor = polygon.options
                                .fillColor;
                            polygon.feature.properties.fillOpacity = polygon
                                .options
                                .fillOpacity;
                            polygon.feature.properties.stroke = polygon.options
                                .stroke;
                            polygon.feature.properties.strokeWeight = polygon
                                .options
                                .weight;
                            this.addLayerPreset('parcel', polygon);
                            polygon.on('mouseover', function (e) {
                                this.setHighlightLayer(polygon)
                            }.bind(this));
                            polygon.on('mouseout', function (e) {
                                this.unsetHighlightLayer(polygon);
                            }.bind(this));
                            polygon.on('click', this.onPolyClick);
                            this.selectGroup("none");


                        } else {
                            await this.addPreset('Parcel', 2, "#ffff00", 3, "fixed",
                                "#ffff00",
                                "0.0");
                            polygon.setStyle(this.presets['parcel'].leafletlayer
                                .metadata);
                            polygon.feature.properties.type = 'Polygon';
                            polygon.feature.properties.preset = 'parcel';
                            polygon.feature.properties.name = "Polygon-" +
                                this.shapeCounter++;
                            polygon.feature.properties.color = polygon.options
                                .color;
                            polygon.feature.properties.fillColor = polygon.options
                                .fillColor;
                            polygon.feature.properties.fillOpacity = polygon
                                .options
                                .fillOpacity;
                            polygon.feature.properties.stroke = polygon.options
                                .stroke;
                            polygon.feature.properties.strokeWeight = polygon
                                .options
                                .weight;
                            this.addLayerPreset('parcel', polygon)
                            polygon.on('mouseover', function (e) {
                                this.setHighlightLayer(polygon);
                            }.bind(this));
                            polygon.on('mouseout', function (e) {
                                this.unsetHighlightLayer(polygon);
                            }.bind(this));
                            polygon.on('click', this.onPolyClick);
                            this.selectGroup("none");
                            this.fitBounds()

                        }
                        this.fitBounds()
                        this.mapState = "editing";


                    } else {
                        this.fitBounds()
                        this.mapState = "editing";
                    }
                    store.set('map/city', addressData.locality)
                    this.mapState = "editing";
                    if (typeof addressData.administrative_area_level_1 != 'undefined')
                        store.set('map/state', addressData.administrative_area_level_1)

                    store.set('map/zip', addressData.postal_code)
                    store.set('map/country', addressData.country)
                    store.set('map/saveAsRequired', true)


                } else {
                    this.fitBounds()
                    this.mapState = "editing";
                    store.set('map/city', addressData.locality)

                    if (typeof addressData.administrative_area_level_1 != 'undefined')
                        store.set('map/state', addressData.administrative_area_level_1)

                    store.set('map/zip', addressData.postal_code)
                    store.set('map/country', addressData.country)
                    store.set('map/saveAsRequired', true)


                }

                this.newMapModel = false;
            } else {
                this.snackbar.text = 'Please type and search for an address.'
                this.snackbar.snackbar = true;
            }
        },
        newBlankDeepMap: async function () {
            const latitude = store.get('map/fileLat')
            const longitude = store.get('map/fileLon')
            const address = store.get('map/address')
            const filename = store.get('map/fileName') || address;
            if (latitude && longitude && address && filename) {

                var lat = latitude;
                var lng = longitude;
                let parcel = await this.getParcel(lat, lng)
                var latlng = L.latLng(lat, lng);
                var bldgicon = LredIcon;
                var marker = L.marker(latlng, {
                    icon: bldgicon
                }).addTo(drawnItems);
                if (typeof parcel != 'undefined') {
                    if (parcel != 0) {

                        var parsed = JSON.parse(parcel);
                        if (parsed.type == "MultiPolygon")
                            var latlngs = L.GeoJSON.coordsToLatLngs(parsed
                                .coordinates[
                                0], 1)
                        else
                            var latlngs = L.GeoJSON.coordsToLatLngs(parsed
                                .coordinates,
                                1)

                        var polygon = L.polygon(latlngs);
                        polygon.feature = polygon.feature || {};
                        polygon.feature.type = "Feature";
                        polygon.feature.properties = polygon.feature.properties || {};

                        var distance = 0;
                        latlngs = latlngs[0];
                        var area = Math.round(turf.area(polygon.toGeoJSON(15)) *
                            10.76391041671)


                        area = numeral(area).format('0,0');
                        polygon.feature.properties.area = area + ' sqft';
                        if (latlngs.length > 2) {
                            for (let i = 0; i < latlngs.length - 1; i++) {
                                distance += latlngs[i].distanceTo(latlngs[i + 1]);
                            }
                            distance += latlngs[latlngs.length - 1].distanceTo(
                                latlngs[
                                0]);
                            distance = distance * 3.28084;
                            distance = numeral(_round(distance, 2)).format('0,0');
                        }
                        polygon.feature.properties.perimeter = distance + ' ft';

                        if (this.presetExists('parcel')) {
                            let preset = this.getPreset('parcel');
                            polygon.setStyle(preset.leafletlayer
                                .metadata);
                            polygon.feature.properties.type = 'Polygon';
                            polygon.feature.properties.preset = 'parcel';
                            polygon.feature.properties.name = "Polygon-" +
                                this.shapeCounter++;
                            polygon.feature.properties.color = polygon.options
                                .color;
                            polygon.feature.properties.fillColor = polygon.options
                                .fillColor;
                            polygon.feature.properties.fillOpacity = polygon
                                .options
                                .fillOpacity;
                            polygon.feature.properties.stroke = polygon.options
                                .stroke;
                            polygon.feature.properties.strokeWeight = polygon
                                .options
                                .weight;
                            this.addLayerPreset('parcel', polygon);
                            polygon.on('mouseover', function (e) {
                                this.setHighlightLayer(polygon)
                            }.bind(this));
                            polygon.on('mouseout', function (e) {
                                this.unsetHighlightLayer(polygon);
                            }.bind(this));
                            polygon.on('click', this.onPolyClick);
                            this.selectGroup("none");


                        } else {
                            await this.addPreset('Parcel', 2, "#ffff00", 3, "fixed",
                                "#ffff00",
                                "0.0");
                            polygon.setStyle(this.presets['parcel'].leafletlayer
                                .metadata);
                            polygon.feature.properties.type = 'Polygon';
                            polygon.feature.properties.preset = 'parcel';
                            polygon.feature.properties.name = "Polygon-" +
                                this.shapeCounter++;
                            polygon.feature.properties.color = polygon.options
                                .color;
                            polygon.feature.properties.fillColor = polygon.options
                                .fillColor;
                            polygon.feature.properties.fillOpacity = polygon
                                .options
                                .fillOpacity;
                            polygon.feature.properties.stroke = polygon.options
                                .stroke;
                            polygon.feature.properties.strokeWeight = polygon
                                .options
                                .weight;
                            this.addLayerPreset('parcel', polygon)
                            polygon.on('mouseover', function (e) {
                                this.setHighlightLayer(polygon);
                            }.bind(this));
                            polygon.on('mouseout', function (e) {
                                this.unsetHighlightLayer(polygon);
                            }.bind(this));
                            polygon.on('click', this.onPolyClick);
                            this.selectGroup("none");
                            this.fitBounds()

                        }
                        this.fitBounds()
                        this.mapState = "editing";


                    } else {
                        this.fitBounds()
                        this.mapState = "editing";
                    }

                    this.mapState = "editing";



                    store.set('map/saveAsRequired', true)


                } else {
                    this.fitBounds()
                    this.mapState = "editing";

                    store.set('map/saveAsRequired', true)


                }

            } else {
                this.snackbar.text = 'Not a valid deep link'
                this.snackbar.snackbar = true;
            }
        },

        async save() {
            this.saveCompleted = false;
            this.saveLoader = true;

            let saveData = this.generateSaveData();
            const postData = {
                accessCode: store.get('map/accessCode'),
                layerid: store.get('map/fileID'),
                bid: this.buildingID,
                name: this.fileName,
                urlkey: store.get('map/urlKey'),
                clientid: store.get('map/takeoffClientVID'),
                orderid: store.get('map/takeoffOrderID'),
                externalprovider: store.get('map/deepLinkProvider'),
                address1: store.get('map/address'),
                address2: store.get('map/address2'),
                city: store.get('map/city'),
                state: store.get('map/state'),
                zip: store.get('map/zip'),
                country: store.get('map/country'),
                geoJSON: JSON.stringify(saveData),
                
            };

            const boundsString = this.getValidBoundsString();
            if (boundsString === false) {
               
                this.saveLoader = false; 
                return;
            }
            postData.bounds = boundsString;

            const requestOptions = {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(postData)
            };

            const req = await fetch(myBaseURL + '/node/maps/save-map', requestOptions);

            const res = await req.json();
            this.saveCompleted = true;
            store.set('map/fileID', res.layerid)
            store.set('map/urlKey', res.urlkey)
            store.set('map/saveAsRequired', false)

            if (this.$store.get('estimator/currentEstimateId')) {
                window.globalLayers = undefined;
                window.globalExternalFlag = undefined;
                window.globalExternalFileID = undefined;
                window.globalExternalFileName = undefined;
                window.globalExternalProvider = undefined;
                window.globalExternalUrlKey = undefined;

                // TODO: switch from nested routers to nested routes to fix parent-inception
                this.$parent.$parent.$parent.$router.push({ path: '/estimator' });
            }
        },
        getValidBoundsString() {
            const bounds = drawnGroups.getBounds();
            if (drawnGroups.getLayers().length === 0 || !bounds.isValid()) {
                alert('Cannot save map. Please add or draw at least one item on the map. Make sure that there is one visible layer on the map.');
                return false;
            }
            return bounds.toBBoxString();
        },
        async saveAs() {
            this.saveAsLoading = true;
            let saveData = this.generateSaveData();
            const requestBody = {
                accessCode: store.get('map/accessCode'),
                layerid: 0,
                orignalLayerId: store.get('map/fileID'),
                bid: this.buildingID,
                name: this.fileName,
                urlkey: "",
                clientid: store.get('map/takeoffClientVID'),
                orderid: store.get('map/takeoffOrderID'),
                externalprovider: store.get('map/deepLinkProvider'),
                address1: store.get('map/address'),
                address2: store.get('map/address2'),
                city: store.get('map/city'),
                state: store.get('map/state'),
                zip: store.get('map/zip'),
                country: store.get('map/country'),
                geoJSON: JSON.stringify(saveData),
                
            };

            const boundsString = this.getValidBoundsString();
             if (boundsString === false) {
                
                this.saveAsLoading = false; 
                return;
            }
            requestBody.bounds = boundsString;

            const req = await fetch(myBaseURL + '/node/maps/save-map', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody),
            });

            const res = await req.json();

            store.set('map/fileID', res.layerid)
            store.set('map/urlKey', res.urlkey)
            store.set('map/saveAsRequired', false)
            this.saveAsDialog = false;
            if (this.$store.get('estimator/currentEstimateId')) {
                window.globalLayers = undefined;
                window.globalExternalFlag = undefined;
                window.globalExternalFileID = undefined;
                window.globalExternalFileName = undefined;
                window.globalExternalProvider = undefined;
                window.globalExternalUrlKey = undefined;

                // TODO: switch from nested routers to nested routes to fix parent-inception
                this.$parent.$parent.$parent.$router.push({ path: '/estimator' });
            }
            this.saveAsLoading = false;
        },
        generateSaveData() {
            var saveData = L.featureGroup();

            for (let preset in this.presets) {
                saveData.addLayer(this.presets[preset].leafletlayer);
            }
            var k = saveData.toGeoJSON(15).features;
            if (k.length > 0) {
                for (let i = 0; i < k.length; i++) {
                    k[i].metadata.baselayer = globalBaseLayerControl.getCurrentBaseLayer();
                    if (globalBaseLayerControl.getCurrentBaseLayer() == 'highres') {
                        const tileProvider = store.get('baselayer/tileProvider');
                        k[i].metadata.date = store.get('baselayer/tileDate');
                        k[i].metadata.provider = tileProvider;

                        if (tileProvider === 'nearmap') {
                            // For Nearmap, store the tile URL as before
                            k[i].metadata.image = store.get('baselayer/tileUrl');
                        } else if (tileProvider === 'eagleview') {
                            // For EagleView, store the layer ID and related metadata
                            k[i].metadata.eagleview_layer_id = store.get('baselayer/eagleviewLayerId');
                            k[i].metadata.eagleview_tile_matrix_set = store.get('baselayer/eagleviewTileMatrixSet');
                            k[i].metadata.eagleview_min_zoom = store.get('baselayer/eagleviewMinZoom');
                            k[i].metadata.eagleview_max_zoom = store.get('baselayer/eagleviewMaxZoom');
                            k[i].metadata.eagleview_purchase_id = store.get('baselayer/eagleviewPurchaseId');
                        } else {
                            // Fallback for other providers - store tile URL if available
                            k[i].metadata.image = store.get('baselayer/tileUrl');
                        }
                    }
                }
            }
            return k;
        },
        toggleLegend(groupId) {

            if (!this.presets[groupId].leafletlayer.metadata.legendVisible) {
                this.presets[groupId].leafletlayer.metadata.legendVisible = true;
                this.presets[groupId].legendVisible = true;

            } else {
                this.presets[groupId].leafletlayer.metadata.legendVisible = false;
                this.presets[groupId].legendVisible = false;
            }
            map.fireEvent('updatelegend');
            this.copyMetadataToFeatureLayer(this.presets[groupId].leafletlayer, groupId);
            this.$forceUpdate()
        },
        selectDefaultGroup() {
            if (this.defaultGroupDialogInput == null) {
                this.snackbar.text = 'Please select a default layer'
                this.snackbar.snackbar = true;
            } else {
                this.selectGroup(this.defaultGroupDialogInput)
                this.defaultGroupDialogResolve(this.defaultGroupDialogInput)
                this.defaultGroupDialog = false;
            }

        },
        selectGroup(groupId) {

            if (!(this.presets[groupId].photogroup || this.presets[groupId].markergroup || this.presets[groupId].textgroup)) {
                if (this.presets[groupId].groupVisible == true) {
                    this.globalSelectedGroup = groupId;
                    this.presets[groupId].leafletlayer.bringToFront();
                }
            }
        },
        chooseDefaultGroup() {
            this.defaultGroupDialogInput = null;
            this.defaultGroupDialog = true;
            return new Promise((resolve, reject) => {
                this.defaultGroupDialogResolve = resolve;
                this.defaultGroupDialogReject = reject;
            });
        },
        getGradientStyle(preset) {
            if (typeof preset.fillColor != 'undefined' && preset.color != 'undefined' && preset.fillOpacity != 'undefined')
                return "background: linear-gradient(to right, " + preset.color + " 4px, " + hexToRGB(
                    preset.fillColor,
                    preset.fillOpacity) + " 4px, " +
                    hexToRGB(preset.fillColor, preset.fillOpacity) +
                    " 12px, transparent 12px, transparent, transparent";
            else
                return "";
        },
        presetExists(preset) {
            return this.presets.hasOwnProperty(preset)
        },
        addPreset: async function (name, grpType, grpLineColor, grpLineStroke, grpLineType,
            grpFillColor,
            grpFillOpacity,
            gnameold = "", measurementType) {

            var presetName = name;
            var g_name = name.toLowerCase();
            g_name = g_name.replace(/\s+/g, '');
            g_name = g_name.replace(/["]/g, "-dq-");
            g_name = g_name.replace(/[']/g, "-sq-");
            g_name = g_name.replace(/[>]/g, "-gt-");
            g_name = g_name.replace(/[<]/g, "-lt-");
            g_name = g_name.replace(/[/]/g, "-bs-");
            g_name = g_name.replace(/[(]/g, "-bst-");
            g_name = g_name.replace(/[)]/g, "-bse-");
            g_name = g_name.replace(/[\\]/g, "-fs-");
            g_name = g_name.replace(/[&]/g, "-and-");
            g_name = g_name.replace(/[,]/g, "-comma-");
            g_name = g_name.replace(/[=]/g, "-equal-");
            g_name = g_name.replace(/[%]/g, "-percent-");
            g_name = g_name.replace(/[+]/g, "-plus-");
            let dashArray = null;
            let dashOffset = null;
            if (grpLineType == 'dashed') {
                dashArray = "10, 10"
            }
            if (grpLineType == 'dotted') {
                dashArray = "1, 5"
            }
            let groupType = null;
            if (grpType == 2) {
                groupType = "Map";
            } else {
                groupType = "Account"
            }
            if (typeof this.presets[g_name] !== 'undefined') {
                var cat = this.presets[g_name].category;
                var flag = true;
                var gid = this.presets[g_name].groupID;
                var llayer = this.presets[g_name].leafletlayer;

            } else {
                var flag = false;
                var cat = "General"
                if (gnameold) {
                    var gid = this.presets[gnameold].groupID;
                } else {
                    var gid = 0;
                }
                var llayer = L.featureGroup();
                if (this.globalSelectedGroup == gnameold)
                    this.globalSelectedGroup = 'none'
            }
            this.presets[g_name] = {
                "shortname": g_name,
                "name": name,
                "category": cat,
                "stroke": grpLineColor,
                "weight": grpLineStroke,
                "strokeOpacity": 1,
                "groupType": groupType,
                "fillColor": grpFillColor,
                "color": grpLineColor,
                "fillOpacity": grpFillOpacity,
                "dashArray": dashArray,
                "vectorgroup": true,
                "measurementType": measurementType,
                "groupVisible": true,
            };
            var category = "General";
            this.presets[g_name].leafletlayer = llayer;
            if (this.presets[g_name].fillColor && this.presets[g_name].color && this.presets[g_name]
                .fillOpacity) {
                var fillColor = this.presets[g_name].fillColor;
                var color = this.presets[g_name].color;
                var alpha = this.presets[g_name].fillOpacity;

            }
            this.copyMetadataToFeatureLayer(this.presets[g_name].leafletlayer, g_name);
            map.fireEvent('updatelegend');

            this.presets[g_name].leafletlayer.setStyle(this.presets[g_name].leafletlayer.metadata);


            if (flag == false) {

                this.presets[g_name].leafletlayer.on('layeradd', this.updateGroups);
                this.presets[g_name].leafletlayer.on('layerremove', this.updateGroups);


                if (typeof this.groups[category] == 'undefined') {
                    this.groups[category] = [];


                }
                let temp = {
                    name: name,
                    value: g_name
                };
                for (let i = this.groups[category].length - 1; i >= 0; i--) {

                    if (this.groups[category][i].value == gnameold) {
                        this.groups[category].splice(i, 1);
                    }
                }

                this.groups[category].push(temp);

                if (gnameold) {
                    for (let nlayer in this.presets[gnameold].leafletlayer._layers) {
                        let glayer = this.presets[gnameold].leafletlayer._layers[nlayer];
                        this.presets[gnameold].leafletlayer.removeLayer(glayer);
                        glayer.setStyle(this.presets[g_name].leafletlayer.metadata)
                        glayer.feature.properties.preset = g_name;
                        this.presets[g_name].leafletlayer.addLayer(glayer);
                        glayer = null;
                    }
                    drawnGroups.removeLayer(this.presets[gnameold].leafletlayer);
                    this.presets[gnameold].leafletlayer = null;
                    delete this.presets[gnameold];
                }
            } else {
                for (let i = this.groups[category].length - 1; i >= 0; i--) {

                    if (this.groups[category][i].value == gnameold) {
                        this.groups[category].splice(i, 1);
                    }
                }
                let temp = {
                    name: name,
                    value: g_name
                };
                this.groups[category].push(temp);

            }


            if (grpType == 1) {
                if (gid != null) {
                    const formBody = new URLSearchParams();
                    formBody.append("accessCode", store.get('map/accessCode'));
                    formBody.append("groupid", gid);
                    formBody.append("name", name);
                    formBody.append("stroke", grpLineColor);
                    formBody.append("weight", grpLineStroke);
                    formBody.append("strokeOpacity", 1);
                    formBody.append("fillColor", grpFillColor);
                    formBody.append("fillOpacity", grpFillOpacity);
                    formBody.append("dashArray", dashArray);
                    formBody.append("measurementType", measurementType.join(','));
                    let response = await fetch(myBaseURL + '/vpics/savemapgroup2', {
                        method: 'POST',
                        body: formBody,
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        }
                    });
                }

            }
            if (flag == false) {
                drawnGroups.addLayer(this.presets[g_name].leafletlayer);
            }
        },
        drawEdgesOld() {
            let turfLayers = [];
            let bedspaceLayers = [];
            let rockbedLayers = [];
            let parkinglotLayers = [];
            let sidewalkLayers = [];
            let publicwalkLayers = [];
            let parkingdeckLayers = [];
            let drivewayLayers = [];
            let drivelaneLayers = [];
            for (var preset in this.presets) {
                //if preset is like 'turf'
                if (this.presets[preset].shortname.indexOf('turfarea') > -1) {
                    //push all layers coordinates into turfLayers
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        turfLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like 'bedspace'
                if (this.presets[preset].shortname.indexOf('bedspace') > -1) {
                    //push all layers coordinates into bedspaceLayers
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        bedspaceLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like 'rockbed'
                if (this.presets[preset].shortname.indexOf('rockbed') > -1) {
                    //push all layers coordinates into rockbedLayers
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        rockbedLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like 'parkinglot'
                if (this.presets[preset].shortname.indexOf('parkinglot') > -1) {
                    //push all layers coordinates into parkinglot
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        parkinglotLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like sidewalk
                if (this.presets[preset].shortname.indexOf('sidewalk') > -1) {
                    //push all layers coordinates into sidewalk
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        sidewalkLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like publicwalk
                if (this.presets[preset].shortname.indexOf('publicwalk') > -1) {
                    //push all layers coordinates into publicwalk
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        publicwalkLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like parkingdeck
                if (this.presets[preset].shortname.indexOf('parkingdeck') > -1) {
                    //push all layers coordinates into parkingdeck
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        parkingdeckLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like driveway
                if (this.presets[preset].shortname.indexOf('driveway') > -1) {
                    //push all layers coordinates into driveway
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        drivewayLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }
                //if preset is like drivelane
                if (this.presets[preset].shortname.indexOf('drivelane') > -1) {
                    //push all layers coordinates into drivelane
                    for (var layer in this.presets[preset].leafletlayer._layers) {
                        drivelaneLayers.push(this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15));
                    }
                }


            }

            let hardEdgeLayers = [];
            let softEdgeLayers = [];
            const cleanAndTruncate = (polygon) => {
                // polygon = turf.cleanCoords(polygon);
                //polygon = turf.unkinkPolygon(polygon);
                return turf.truncate(polygon, { precision: 7 });
            };
            function sortCoordinates(coordinates) {
                const sortedCoordinates = [coordinates[0]];
                coordinates.shift();

                while (coordinates.length > 0) {
                    const lastPoint = sortedCoordinates[sortedCoordinates.length - 1];
                    const closestPoint = coordinates.reduce((prev, curr) => {
                        const prevDistance = turf.distance(lastPoint, prev);
                        const currDistance = turf.distance(lastPoint, curr);
                        return prevDistance < currDistance ? prev : curr;
                    });
                    const closestPointIndex = coordinates.findIndex(coord => coord === closestPoint);
                    sortedCoordinates.push(closestPoint);
                    coordinates.splice(closestPointIndex, 1);
                }

                return sortedCoordinates;
            }
            function sortCoordinates2(line, polygon) {
                //convert the polygon to featurecollection of points
                const polyPoints = turf.explode(polygon);
                const linePoints = line;
                let lineCoords = linePoints.features.map(feature => feature.geometry.coordinates)
                let polyCoords = polyPoints.features.map(feature => feature.geometry.coordinates)
                //console.log(JSON.stringify(polyCoords))
                let revisedLine = [];
                // find the first lineCoord in polyCoords array

                let indexes = []
                for (let i = 0; i < lineCoords.length; i++) {
                    let nearestPoint = turf.nearestPoint(turf.point(lineCoords[i]), polyPoints);
                    if (nearestPoint.properties.distanceToPoint < 0.0001)
                        indexes[i] = nearestPoint.properties.featureIndex;

                }
                // console.log(JSON.stringify(indexes))
                //sort the indexes in ascending order
                indexes.sort(function (a, b) { return a - b });

                //make a featurecollection of points from polycoords of the indexes selected
                let sortedPolyCoords = [];
                for (let i = 0; i < indexes.length; i++) {
                    sortedPolyCoords.push(polyCoords[indexes[i]]);
                }

                //check if any are undefined
                if (sortedPolyCoords.includes(undefined)) {
                    return [];
                }
                //console.log(JSON.stringify(sortedPolyCoords))
                return sortedPolyCoords;















            }


            for (let turfLayer of turfLayers) {
                //if not polygon then continue
                if (turfLayer.geometry.type != 'Polygon') continue;
                //logic for soft edge
                for (let bedspaceLayer of bedspaceLayers) {
                    if (bedspaceLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const bedspaceLayerCoords = bedspaceLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let bedspaceLayerPolygon = turf.polygon(bedspaceLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        bedspaceLayerPolygon = cleanAndTruncate(bedspaceLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, bedspaceLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, bedspaceLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                softEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }

                for (let rockbedLayer of rockbedLayers) {
                    if (rockbedLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const rockbedLayerCoords = rockbedLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let rockbedLayerPolygon = turf.polygon(rockbedLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        rockbedLayerPolygon = cleanAndTruncate(rockbedLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, rockbedLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, rockbedLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                softEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }

                //logic for hard edge
                for (let parkinglotLayer of parkinglotLayers) {
                    if (parkinglotLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const parkinglotLayerCoords = parkinglotLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let parkinglotLayerPolygon = turf.polygon(parkinglotLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        parkinglotLayerPolygon = cleanAndTruncate(parkinglotLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, parkinglotLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, parkinglotLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                hardEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }
                for (let sidewalkLayer of sidewalkLayers) {
                    if (sidewalkLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const sidewalkLayerCoords = sidewalkLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let sidewalkLayerPolygon = turf.polygon(sidewalkLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        sidewalkLayerPolygon = cleanAndTruncate(sidewalkLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, sidewalkLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, sidewalkLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                hardEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }

                for (let publicwalkLayer of publicwalkLayers) {
                    if (publicwalkLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const publicwalkLayerCoords = publicwalkLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let publicwalkLayerPolygon = turf.polygon(publicwalkLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        publicwalkLayerPolygon = cleanAndTruncate(publicwalkLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, publicwalkLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, publicwalkLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                hardEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }
                for (let parkingdeckLayer of parkingdeckLayers) {
                    if (parkingdeckLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const parkingdeckLayerCoords = parkingdeckLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let parkingdeckLayerPolygon = turf.polygon(parkingdeckLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        parkingdeckLayerPolygon = cleanAndTruncate(parkingdeckLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, parkingdeckLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, parkingdeckLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                hardEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }
                for (let drivewayLayer of drivewayLayers) {
                    if (drivewayLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const drivewayLayerCoords = drivewayLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let drivewayLayerPolygon = turf.polygon(drivewayLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        drivewayLayerPolygon = cleanAndTruncate(drivewayLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, drivewayLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, drivewayLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                hardEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }
                for (let drivelaneLayer of drivelaneLayers) {
                    if (drivelaneLayer.geometry.type !== 'Polygon') continue;

                    const turfLayerCoords = turfLayer.geometry.coordinates;
                    const drivelaneLayerCoords = drivelaneLayer.geometry.coordinates;

                    try {
                        let turfLayerPolygon = turf.polygon(turfLayerCoords);
                        let drivelaneLayerPolygon = turf.polygon(drivelaneLayerCoords);

                        turfLayerPolygon = cleanAndTruncate(turfLayerPolygon);
                        drivelaneLayerPolygon = cleanAndTruncate(drivelaneLayerPolygon);

                        const difference = turf.lineIntersect(turfLayerPolygon, drivelaneLayerPolygon);

                        if (difference && difference.features.length > 1) {
                            let sortedCoordinates = sortCoordinates2(difference, drivelaneLayerPolygon);
                            if (sortedCoordinates.length > 1) {
                                let feature = turf.lineString(sortedCoordinates);
                                hardEdgeLayers.push(feature);
                            }
                        }
                    } catch (e) {
                        console.log(e);
                        continue;
                    }
                }

            }

            let softEdgeGroup = null;
            let hardEdgeGroup = null;
            //find softedge layer in drawnGroups and hardedge layer in this.presets
            for (let preset in this.presets) {
                if (this.presets[preset].shortname.indexOf('softedge') > -1) {
                    softEdgeGroup = this.presets[preset];
                }
                if (this.presets[preset].shortname.indexOf('hardedge') > -1) {
                    hardEdgeGroup = this.presets[preset];
                }
            }

            //process softEdgeLayers
            for (let softEdgeLayer of softEdgeLayers) {
                //add the layers into softEdgeGroup inherit styling from softEdgeGroup
                if (softEdgeGroup) {

                    //first check if a line with the same coordinates already exists in softEdgeGroup
                    let layerFound = false;
                    for (let layer in softEdgeGroup.leafletlayer._layers) {
                        let layerCoords = softEdgeGroup.leafletlayer._layers[layer].toGeoJSON(15);

                        if (layerCoords.geometry) {
                            let layerLine = turf.feature(layerCoords.geometry);

                            let softEdgeLine = turf.feature(softEdgeLayer.geometry);
                            let difference = turf.lineOverlap(layerLine, softEdgeLine, { tolerance: 0.001 });
                            if (difference.features.length > 0) {
                                layerFound = true;
                                break;
                            }
                            /* if (isWithinTolerance(layerCoords.geometry.coordinates, softEdgeLayer.geometry.coordinates, TOLERANCE)) {
                                 layerFound = true;
                                 break;
                             }*/
                        }
                    }
                    if (layerFound) continue;

                    let geojsonLayer = L.polyline(L.GeoJSON.coordsToLatLngs(turf.getCoords(softEdgeLayer)), JSON.parse(JSON.stringify(_.omitBy(_.pick(softEdgeGroup, ['color', 'strokeOpacity'
                    ]), _.isObject))));

                    geojsonLayer.on('click', globalVueMapInstance.onPolyClick);
                    var feature = geojsonLayer.feature = geojsonLayer.feature || {};
                    feature.type = "Feature";
                    feature.properties = feature.properties || {};
                    this.shapeCounter += 1;
                    feature.properties["id"] = this.shapeCounter;
                    feature.properties["name"] = '';
                    feature.properties["preset"] = softEdgeGroup.shortname;
                    globalVueMapInstance.updateLayerProps(geojsonLayer);
                    this.addLayerPreset(softEdgeGroup.shortname, geojsonLayer)



                    this.updateLayerInPanel(
                        geojsonLayer.feature.properties
                            .preset,
                        geojsonLayer);

                }
            }
            //process hardEdgeLayers
            for (let hardEdgeLayer of hardEdgeLayers) {

                if (hardEdgeGroup) {


                    let layerFound = false;
                    for (let layer in hardEdgeGroup.leafletlayer._layers) {
                        let layerCoords = hardEdgeGroup.leafletlayer._layers[layer].toGeoJSON(15);

                        if (layerCoords.geometry) {
                            if (layerCoords.geometry) {
                                let layerLine = turf.feature(layerCoords.geometry);
                                let hardEdgeLine = turf.feature(hardEdgeLayer.geometry);
                                let difference = turf.lineOverlap(layerLine, hardEdgeLine, { tolerance: 0.001 });
                                if (difference.features.length > 0) {
                                    layerFound = true;
                                    break;
                                }

                            }
                        }
                    }
                    if (layerFound) continue;

                    let geojsonLayer = L.polyline(L.GeoJSON.coordsToLatLngs(turf.getCoords(hardEdgeLayer)), JSON.parse(JSON.stringify(_.omitBy(_.pick(hardEdgeGroup, ['color', 'strokeOpacity'
                    ]), _.isObject))));


                    geojsonLayer.on('click', globalVueMapInstance.onPolyClick);
                    var feature = geojsonLayer.feature = geojsonLayer.feature || {};
                    feature.type = "Feature";
                    feature.properties = feature.properties || {};
                    this.shapeCounter += 1;
                    feature.properties["id"] = this.shapeCounter;
                    feature.properties["name"] = '';
                    feature.properties["preset"] = hardEdgeGroup.shortname;
                    globalVueMapInstance.updateLayerProps(geojsonLayer);
                    this.addLayerPreset(hardEdgeGroup.shortname, geojsonLayer)
                    this.updateLayerInPanel(
                        geojsonLayer.feature.properties
                            .preset,
                        geojsonLayer);

                }
            }
        },
        drawEdges() {
          
            const surfacePolygons = {
                turf: this.collectPolygons('turfarea'),
                bedspace: this.collectPolygons('bedspace'),
                rockbed: this.collectPolygons('rockbed'),
                parkinglot: this.collectPolygons('parkinglot'),
                sidewalk: this.collectPolygons('sidewalk'),
                publicwalk: this.collectPolygons('publicwalk'),
                parkingdeck: this.collectPolygons('parkingdeck'),
                driveway: this.collectPolygons('driveway'),
                drivelane: this.collectPolygons('drivelane')
            };
            
       
            const edgeDefinitions = [
              
                ['turf', 'bedspace', 'softedge'],
                ['turf', 'rockbed', 'softedge'],
                ['turf', 'parkinglot', 'hardedge'],
                ['turf', 'sidewalk', 'hardedge'],
                ['turf', 'publicwalk', 'hardedge'],
                ['turf', 'parkingdeck', 'hardedge'],
                ['turf', 'driveway', 'hardedge'],
                ['turf', 'drivelane', 'hardedge']
            ];
            
           
            const edges = {
                softedge: [],
                hardedge: []
            };
            
            
            edgeDefinitions.forEach(([surface1, surface2, edgeType]) => {
                if (!surfacePolygons[surface1] || !surfacePolygons[surface2]) return;
                
              
                surfacePolygons[surface1].forEach(poly1 => {
                    surfacePolygons[surface2].forEach(poly2 => {
                        const sharedBoundary = this.extractSharedBoundary(poly1, poly2);
                        if (sharedBoundary) {
                            edges[edgeType].push(sharedBoundary);
                        }
                    });
                });
            });
            
           
            Object.keys(edges).forEach(edgeType => {
                const edgeGroup = this.findPresetByShortname(edgeType);
                if (!edgeGroup) return;
                
                edges[edgeType].forEach(edge => {
                    if (!this.edgeAlreadyExists(edge, edgeGroup)) {
                        this.addEdgeToMap(edge, edgeGroup);
                    }
                });
            });
        },
        
        
        collectPolygons(surfaceType) {
            const polygons = [];
            for (const preset in this.presets) {
                if (this.presets[preset].shortname.indexOf(surfaceType) > -1) {
                    for (const layer in this.presets[preset].leafletlayer._layers) {
                        const geoJson = this.presets[preset].leafletlayer._layers[layer].toGeoJSON(15);
                        if (geoJson.geometry && geoJson.geometry.type === 'Polygon') {
                            polygons.push(turf.polygon(geoJson.geometry.coordinates));
                        }
                    }
                }
            }
            return polygons;
        },
        
        
        findPresetByShortname(shortname) {
            for (const preset in this.presets) {
                if (this.presets[preset].shortname.indexOf(shortname) > -1) {
                    return this.presets[preset];
                }
            }
            return null;
        },
        
        
        edgeAlreadyExists(edge, edgeGroup) {
            for (const layer in edgeGroup.leafletlayer._layers) {
                const existingEdge = edgeGroup.leafletlayer._layers[layer].toGeoJSON(15);
                if (existingEdge.geometry && existingEdge.geometry.type === 'LineString') {
                    // Use turf.lineOverlap with appropriate tolerance
                    const overlap = turf.lineOverlap(edge, turf.lineString(existingEdge.geometry.coordinates), {
                        tolerance: 0.0001
                    });
                    if (overlap.features.length > 0) {
                        return true;
                    }
                }
            }
            return false;
        },
        
    
        addEdgeToMap(edge, edgeGroup) {
            const geojsonLayer = L.polyline(
                L.GeoJSON.coordsToLatLngs(turf.getCoords(edge)), 
                JSON.parse(JSON.stringify(_.omitBy(_.pick(edgeGroup, ['color', 'strokeOpacity']), _.isObject)))
            );
            
            geojsonLayer.on('click', globalVueMapInstance.onPolyClick);
            const feature = geojsonLayer.feature = geojsonLayer.feature || {};
            feature.type = "Feature";
            feature.properties = feature.properties || {};
            this.shapeCounter += 1;
            feature.properties["id"] = this.shapeCounter;
            feature.properties["name"] = '';
            feature.properties["preset"] = edgeGroup.shortname;
            globalVueMapInstance.updateLayerProps(geojsonLayer);
            this.addLayerPreset(edgeGroup.shortname, geojsonLayer);
            this.updateLayerInPanel(geojsonLayer.feature.properties.preset, geojsonLayer);
        },
        
        
        extractSharedBoundary(polygon1, polygon2) {
           
            const ring1 = turf.getCoords(polygon1)[0]; 
            const ring2 = turf.getCoords(polygon2)[0]; 
            
           
            const segments1 = new Map();
            for (let i = 0; i < ring1.length - 1; i++) {
                const start = ring1[i];
                const end = ring1[i + 1];
                const key = this.createSegmentKey(start, end);
                segments1.set(key, [start, end]);
            }
            
            
            const sharedSegments = [];
            for (let i = 0; i < ring2.length - 1; i++) {
                const start = ring2[i];
                const end = ring2[i + 1];
                const key1 = this.createSegmentKey(start, end);
                const key2 = this.createSegmentKey(end, start);
                
                if (segments1.has(key1) || segments1.has(key2)) {
                    sharedSegments.push([start, end]);
                }
            }
            
            
            if (sharedSegments.length > 0) {
                const connectedLines = this.connectSegments(sharedSegments);
                if (connectedLines.length > 0) {
                    connectedLines.sort((a, b) => b.length - a.length);
                    return turf.lineString(connectedLines[0]);
                }
            }
            
            
            try {
                
                if (turf.booleanDisjoint(polygon1, polygon2)) {
                    return null;
                }
                
                
                const buffer = turf.buffer(polygon1, 0.00001, {units: 'kilometers'});
                
               
                const intersection = turf.intersect(buffer, polygon2);
                if (!intersection) return null;
                
                
                const boundary = turf.polygonToLine(intersection);
                
                
                if (boundary.geometry.type === 'LineString') {
                    return boundary;
                }
                
                
                if (boundary.geometry.type === 'MultiLineString') {
                    const lines = boundary.geometry.coordinates.map(coords => turf.lineString(coords));
                    lines.sort((a, b) => turf.length(b) - turf.length(a));
                    return lines[0];
                }
                
                return null;
            } catch (e) {
                console.error("Error in buffer-based boundary detection:", e);
                return null;
            }
        },
        
        
       
        createSegmentKey(point1, point2) {
            
            const [a, b] = [point1, point2].sort((p1, p2) => {
                if (p1[0] !== p2[0]) return p1[0] - p2[0];
                return p1[1] - p2[1];
            });
            
            
            return `${a[0].toFixed(7)},${a[1].toFixed(7)}_${b[0].toFixed(7)},${b[1].toFixed(7)}`;
        },
        
       
        connectSegments(segments) {
            if (segments.length === 0) return [];
            if (segments.length === 1) return [segments[0]];
            
            // Create a copy to work with
            const remainingSegments = segments.map(s => [...s]);
            const connectedLines = [];
            
            while (remainingSegments.length > 0) {
                
                let currentLine = [...remainingSegments[0]];
                remainingSegments.splice(0, 1);
                
                let segmentAdded = true;
                while (segmentAdded) {
                    segmentAdded = false;
                    
                   
                    for (let i = 0; i < remainingSegments.length; i++) {
                        const segment = remainingSegments[i];
                        
                        
                        if (this.arePointsEqual(segment[1], currentLine[0])) {
                            
                            currentLine.unshift(segment[0]);
                            remainingSegments.splice(i, 1);
                            segmentAdded = true;
                            break;
                        }
                        
                        else if (this.arePointsEqual(segment[0], currentLine[0])) {
                            
                            currentLine.unshift(segment[1]);
                            remainingSegments.splice(i, 1);
                            segmentAdded = true;
                            break;
                        }
                       
                        else if (this.arePointsEqual(segment[0], currentLine[currentLine.length - 1])) {
                            
                            currentLine.push(segment[1]);
                            remainingSegments.splice(i, 1);
                            segmentAdded = true;
                            break;
                        }
                        
                        else if (this.arePointsEqual(segment[1], currentLine[currentLine.length - 1])) {
                           
                            currentLine.push(segment[0]);
                            remainingSegments.splice(i, 1);
                            segmentAdded = true;
                            break;
                        }
                    }
                }
                
              
                connectedLines.push(currentLine);
            }
            
            return connectedLines;
        },
        
       
        arePointsEqual(point1, point2, tolerance = 0.0000001) {
            return Math.abs(point1[0] - point2[0]) < tolerance && 
                   Math.abs(point1[1] - point2[1]) < tolerance;
        },        
        getPreset(preset) {
            return this.presets[preset];
        },
        getPresets() {
            return this.presets;
        },
        deletePreset(preset) {
            for (let nlayer in this.presets[preset].leafletlayer._layers) {
                let glayer = this.presets[preset].leafletlayer._layers[nlayer];
                this.presets[preset].leafletlayer.removeLayer(glayer);
                glayer = null;
            }
            drawnGroups.removeLayer(this.presets[preset].leafletlayer);
            this.presets[preset].leafletlayer = null;
            for (let grop in this.groups) {
                for (let i = this.groups[grop].length - 1; i >= 0; i--) {
                    if (this.groups[grop][i].value == preset) {
                        this.groups[grop].splice(i, 1);
                    }
                }
            }
            if (typeof this.presets[preset].groupID != 'undefined' && typeof globalServerGroups != 'undefined') {
                const id = this.presets[preset].groupID;
                const index = globalServerGroups.findIndex(obj => obj['groupID'] == id);
                globalServerGroups = index >= 0 ? [
                    ...globalServerGroups.slice(0, index),
                    ...globalServerGroups.slice(index + 1)
                ] : globalServerGroups;
                let grptype = this.presets[preset].groupType;
                console.log(grptype)
                if (grptype == "Account") {
                    if (id != null) {
                        const formBody = new URLSearchParams();
                        formBody.append("accessCode", store.get('map/accessCode'));
                        formBody.append("groupid", id);
                        fetch(myBaseURL + '/vpics/deletemapgroup', {
                            method: 'POST',
                            body: formBody,
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded'
                            }
                        });
                    }
                }
            }
            delete this.presets[preset];


        },
        addLayerPreset(preset, layer) {
            this.presets[preset].leafletlayer.addLayer(layer);
        },
        removeLayerPreset(preset, layer) {
            this.presets[preset].leafletlayer.removeLayer(layer);
        },
        drawStart(event) {
            switch (event.shape) {
                case 'Rectangle':
                    //map.pm.setPathOptions(_.omitBy(presets[globalSelectedGroup].leafletlayer.metadata, _.isObject));
                    event.workingLayer.setStyle(_.omitBy(this.presets[this.globalSelectedGroup].leafletlayer.metadata, _
                        .isObject));
                    break;
                case 'Poly':
                    event.workingLayer.setStyle(_.omitBy(this.presets[this.globalSelectedGroup].leafletlayer.metadata, _
                        .isObject));
                    break;
                case 'Circle':
                case 'CircleMarker':
                    event.workingLayer.setStyle(_.omitBy(this.presets[this.globalSelectedGroup].leafletlayer.metadata, _
                        .isObject));
                    break;
                case 'Line':

                    event.workingLayer.setStyle(_.omitBy(_.pick(this.presets[this.globalSelectedGroup], ['color', 'strokeOpacity'
                    ]), _.isObject))
                    break;
                default:
                    break;
            }
        },
        cleanSavedData(sData) {
            let myData = JSON.parse(JSON.stringify(sData));
            return new Promise(resolve => {
                let iDone = false;
                for (let lll = 0; lll < myData.length; lll++) {
                    let sDataFeature = myData[lll].features;
                    if (typeof sDataFeature != 'undefined') {

                        for (let jjj = 0; jjj < sDataFeature.length; jjj++) {
                            if (typeof sDataFeature[jjj].geometry != 'undefined') {
                                if (sDataFeature[jjj].geometry.type == 'Polygon') {
                                    let sDataCoords = sDataFeature[jjj].geometry.coordinates;
                                    sDataCoords = this.removeNulls(sDataCoords)
                                }
                                if (sDataFeature[jjj].geometry.type == "MultiPolygon") {
                                    let sDataCoords = sDataFeature[jjj].geometry.coordinates;
                                    sDataCoords = this.removeNulls(sDataCoords)
                                }

                            }
                        }
                    }
                }
                resolve(myData);
            });
        },
        updateData: async function () {
            if (store.get('map/takeoffLink') == false) {
                const formData = new FormData();
                formData.append('accessCode', store.get('map/accessCode'));
                const formData2 = new FormData();
                formData2.append('accessCode', store.get('map/accessCode'));
                const [layers, sites, siteLeas] = await Promise.all([
                    fetch(myBaseURL + '/node/maps/map-layers', { method: 'POST', body: formData2 }),
                    fetch(myBaseURL + '/node/sites/get-buildings', { method: 'POST', body: formData }),
                    fetch(myBaseURL + '/node/sites/get-buildings-only-site-leads', { method: 'POST', body: formData })
                ]);
                const [layersJson, sitesJson, siteLeadsJson] = await Promise.all([
                    layers.json(),
                    sites.json(),
                    siteLeas.json()
                ]);
                let parsedFiles = layersJson.map(function (v) {
                    v.datetime = moment.unix(v.DateTime).format("MM/DD/YY hh:mm a");
                    v.modified = v.DateTime

                    let search = sitesJson.buildings.find(({ mb_id }) => mb_id == v.BuildingID);
                    v.sitename = typeof search == 'undefined' ? "Unassigned" : search.mb_nickname;
                    return v;
                });

                Vue.set(this, 'files', parsedFiles.filter(file => file.AutoSave == 0));

                this.buildings = [...sitesJson.buildings, ...siteLeadsJson.buildings];
            }
        },
        clearScene() {

            drawnGroups.clearLayers();
            drawnItems.clearLayers();


            this.globalSelectedGroup = 'none';


            for (let i = 0; i < this.animations.length; i++) {
                map.removeLayer(this.animations[i][2]);
            }
            for (let i = 0; i < this.arrowHeads.length; i++) {
                map.removeLayer(this.arrowHeads[i][2]);
            }

            this.animations = [];
            this.arrowHeads = [];


            this.initializePresets();


            fetch(myBaseURL + '/vpics/clearsessionimages', { method: 'POST' })


        },
        removeNulls(obj) {
            let isArray = obj instanceof Array;
            for (let k in obj) {
                if (obj[k] === null) isArray ? obj.splice(k, 1) : delete obj[k];
                else if (typeof obj[k] == "object") this.removeNulls(obj[k]);
                if (isArray && obj.length == k) this.removeNulls(obj);
            }
            return obj;
        },
        parseColor(color) {
            var arr = [];
            color = color.split(",");
            for (let i = 0; i < color.length; i++)
                arr.push(parseFloat(color[i]));

            return {
                hex: "#" + arr.slice(0, 3).map(this.toHex).join(""),
                opacity: arr.length == 4 ? arr[3] : 1
            };
        },
        toHex(int) {
            var hex = int.toString(16);
            return hex.length == 1 ? "0" + hex : hex;
        },
        initializePresets() {
            if (typeof globalServerGroups == 'undefined') {
                let takeoffs = JSON.parse(globalTakeoffs);

                window.globalServerGroups = [];
                for (var i = 0; i < takeoffs.length; i++) {
                    let group = {};
                    group.groupName = takeoffs[i].title;
                    if (typeof takeoffs[i].outline_color !== 'undefined')
                        group.stroke = this.parseColor(takeoffs[i].outline_color).hex;
                    if (typeof takeoffs[i].fill_color !== 'undefined')
                        group.fillColor = this.parseColor(takeoffs[i].fill_color).hex;
                    if (typeof takeoffs[i].alpha_fill !== 'undefined')
                        group.fillOpacity = takeoffs[i].alpha_fill;
                    if (typeof takeoffs[i].alpha_color !== 'undefined')
                        group.strokeOpacity = takeoffs[i].alpha_color;
                    group.weight = 3;
                    group.takeoff = true;
                    window.globalServerGroups.push(group);
                }
            }
            this.presets = {};

            this.groups = [];
            for (let i = 0; i < globalServerGroups.length; i++) {
                var g_name = globalServerGroups[i].groupName.toLowerCase();

                g_name = g_name.replace(/\s+/g, '');
                g_name = g_name.replace(/["]/g, "-dq-");
                g_name = g_name.replace(/[']/g, "-sq-");
                g_name = g_name.replace(/[>]/g, "-gt-");
                g_name = g_name.replace(/[<]/g, "-lt-");
                g_name = g_name.replace(/[/]/g, "-bs-");
                g_name = g_name.replace(/[(]/g, "-bst-");
                g_name = g_name.replace(/[)]/g, "-bse-");
                g_name = g_name.replace(/[\\]/g, "-fs-");
                g_name = g_name.replace(/[&]/g, "-and-");
                g_name = g_name.replace(/[,]/g, "-comma-");
                g_name = g_name.replace(/[=]/g, "-equal-");
                g_name = g_name.replace(/[%]/g, "-percent-");
                g_name = g_name.replace(/[+]/g, "-plus-");
                var category = globalServerGroups[i].category;
                if (typeof category !== 'undefined') {
                    if (category !== null)
                        category = category.replace(/[\']/g, "'");
                }
                var group_name = globalServerGroups[i].groupName;
                if (typeof group_name !== 'undefined') {
                    group_name = group_name.replace(/[\']/g, "'");
                    group_name = group_name.replace(/[\\"]/g, '"');
                }
                var takeoff = globalServerGroups[i].takeoff
                if (typeof takeoff !== 'undefined') {
                    var group_type = "Map";
                } else {
                    var group_type = "Account";
                }
                this.presets[g_name] = {
                    "shortname": g_name,
                    "groupID": globalServerGroups[i].groupID,
                    "name": group_name,
                    "stroke": true,
                    "fill": true,
                    "color": globalServerGroups[i].stroke,
                    "weight": globalServerGroups[i].weight,
                    "opacity": globalServerGroups[i].strokeOpacity,
                    "fillColor": globalServerGroups[i].fillColor,
                    "fillOpacity": globalServerGroups[i].fillOpacity,
                    "groupType": group_type,
                    "groupIcon": globalServerGroups[i].groupIcon,
                    "groupdefault": globalServerGroups[i].groupdefault,
                    "dashArray": globalServerGroups[i].dashArray,
                    "dashOffset": globalServerGroups[i].dashOffset,
                    "measurementType": globalServerGroups[i].measurementType ? globalServerGroups[i].measurementType.split(',') : ["AREA"],
                    "category": category,
                    "groupVisible": true,
                    "legendVisible": false,
                    "labelVisible": false,
                    "vectorgroup": true,
                    "layervisible": "activated",
                    "labelvisible": "deactivated",
                    "legendvisible": "deactivated",
                    "leafletlayer": L.featureGroup()
                };
                this.presets[g_name].leafletlayer.on('layeradd', this.updateGroups);
                this.presets[g_name].leafletlayer.on('layerremove', this.updateGroups);

                if (this.presets[g_name].fillColor == null) {
                    this.presets[g_name].fillColor = globalServerGroups[i].stroke;
                }

                if (category == null) {
                    category = "General"
                }
                if (typeof this.groups[category] == 'undefined') {
                    Vue.set(this.groups, category, [])


                }

                var temp = {
                    name: group_name,
                    value: g_name
                };
                this.groups[category].push(temp);


                this.copyMetadataToFeatureLayer(this.presets[g_name].leafletlayer, g_name);
                L.Util.setOptions(this.presets[g_name].leafletlayer, {
                    style: this.presets[g_name].leafletlayer.metadata
                });
                drawnGroups.addLayer(this.presets[g_name].leafletlayer);
            }

            this.presets["none"] = {
                "shortname": "none",
                "name": "Ungrouped",
                "layervisible": "activated",
                "labelvisible": "deactivated",
                "legendvisible": "deactivated",
                "stroke": true,
                "fill": true,
                "groupVisible": true,
                "legendVisible": false,
                "color": "#00ff00",
                "groupType": "Auto",
                "weight": "2",
                "vectorgroup": true,
                "strokeOpacity": "1",
                "fillColor": "#00ff00",
                "fillOpacity": "0.3",
                "leafletlayer": L.featureGroup()
            };
            let feature = this.presets["none"].leafletlayer.feature = this.presets["none"].leafletlayer.feature || {};
            feature.type = "FeatureGroup";
            feature.properties = feature.properties || {};
            this.presets["none"].leafletlayer.on('layeradd', this.updateGroups);
            this.presets["none"].leafletlayer.on('layerremove', this.updateGroups);
            this.copyMetadataToFeatureLayer(this.presets["none"].leafletlayer, 'none');
            drawnGroups.addLayer(this.presets['none'].leafletlayer);
            this.presets["hole"] = {
                "stroke": "#556666",
                "weight": 3,
                "strokeOpacity": 1,
                "shortname": "hole",
                "name": "hole",
                "groupType": "Auto",
                "groupVisible": false,
                "vectorgroup": true,
                "legendVisible": false,
                "fillColor": "rgb(255, 153, 0)",
                "fillOpacity": 0.8,
                "leafletlayer": L.featureGroup()
            };
            feature = this.presets["hole"].leafletlayer.feature = this.presets["hole"].leafletlayer.feature || {};
            feature.type = "FeatureGroup";
            feature.properties = feature.properties || {};
            this.presets["hole"].leafletlayer.on('layeradd', this.updateGroups);
            this.presets["hole"].leafletlayer.on('layerremove', this.updateGroups);
            this.copyMetadataToFeatureLayer(this.presets["hole"].leafletlayer, 'hole');
            drawnGroups.addLayer(this.presets['hole'].leafletlayer);

            this.presets["photos"] = {
                "shortname": "photos",
                "name": "Photos",
                "layervisible": "activated",
                "labelvisible": "deactivated",
                "legendvisible": "deactivated",
                "groupType": "Auto",
                "photogroup": true,
                "groupVisible": true,
                "leafletlayer": L.featureGroup()
            };
            this.presets["photos"].leafletlayer.on('layeradd', this.updateGroups);
            this.presets["photos"].leafletlayer.on('layerremove', this.updateGroups);
            this.copyMetadataToFeatureLayer(this.presets["photos"].leafletlayer, 'photos');
            drawnGroups.addLayer(this.presets['photos'].leafletlayer);
            this.presets["textinternal"] = {
                "shortname": "textinternal",
                "name": "Text",
                "textgroup": true,
                "groupVisible": true,
                "groupType": "Auto",
                "layervisible": "activated",
                "labelvisible": "deactivated",
                "legendvisible": "deactivated",
                "leafletlayer": L.featureGroup()
            };
            this.presets["textinternal"].leafletlayer.on('layeradd', this.updateGroups);
            this.presets["textinternal"].leafletlayer.on('layerremove', this.updateGroups);
            this.copyMetadataToFeatureLayer(this.presets["textinternal"].leafletlayer, 'textinternal');
            drawnGroups.addLayer(this.presets['textinternal'].leafletlayer);

            this.presets["markers"] = {
                "shortname": "markers",
                "name": "Markers",
                "markergroup": true,
                "groupVisible": true,
                "groupType": "Auto",
                "layervisible": "activated",
                "labelvisible": "deactivated",
                "legendvisible": "deactivated",
                "leafletlayer": L.featureGroup()
            };
            this.presets["markers"].leafletlayer.on('layeradd', this.updateGroups);
            this.presets["markers"].leafletlayer.on('layerremove', this.updateGroups);
            this.copyMetadataToFeatureLayer(this.presets["markers"].leafletlayer, 'markers');
            drawnGroups.addLayer(this.presets['markers'].leafletlayer);

        },
        copyMetadataToFeatureLayer(layer, group) {
            layer.metadata = layer.metadata || {}
            for (var property in this.presets[group]) {
                if (this.presets[group].hasOwnProperty(property)) {
                    if (property != 'leafletlayer') {
                        if (property == 'legendVisible') {
                            if (typeof layer.metadata[property] == 'undefined')
                                layer.metadata[property] = this.presets[group][property];
                        } else
                            layer.metadata[property] = this.presets[group][property]
                    }

                }
            }
        },
        calcAreaPerimTotals(layers) {
            let myArea = 0,
                myPerim = 0,
                myItems = 0;


            if (layers._layers !== undefined) {
                myItems = Object.keys(layers._layers).length;

                for (var key in layers._layers) {
                    if (layers._layers.hasOwnProperty(key)) {
                        if (typeof layers._layers[key]._layers != 'undefined') {
                            for (var subkey in layers._layers[key]._layers) {
                                if (layers._layers[key]._layers.hasOwnProperty(subkey)) {

                                    if(!layers._layers[key]._layers[subkey].feature)
                                        continue;
                                    if (typeof layers._layers[key]._layers[subkey].feature.properties != 'undefined') {
                                        if (typeof layers._layers[key]._layers[subkey].feature != 'undefined') {
                                            if (typeof layers._layers[key]._layers[subkey].feature.properties.area != 'undefined')
                                                myArea = myArea + parseFloat(layers._layers[key]._layers[subkey].feature
                                                    .properties
                                                    .area.replace(/[^0-9]/g,
                                                        ''))
                                            if (typeof layers._layers[key]._layers[subkey].feature.properties.perimeter != 'undefined')
                                                myPerim = myPerim + parseFloat(layers._layers[key]._layers[subkey].feature
                                                    .properties
                                                    .perimeter.replace(
                                                        /[^0-9]/g,
                                                        ''))
                                            // myItems++;
                                        }
                                    }
                                }
                            }
                        } else {
                            if (layers._layers[key].feature.properties !== undefined) {
                                if (layers._layers[key].feature.properties.area !== undefined)
                                    myArea = myArea + parseFloat(layers._layers[key].feature.properties.area.replace(
                                        /[^0-9]/g, ''))
                                if (layers._layers[key].feature.properties.perimeter !== undefined)
                                    myPerim = myPerim + parseFloat(layers._layers[key].feature.properties.perimeter.replace(
                                        /[^0-9]/g, ''))
                                // myItems++;
                            }
                        }
                    }
                }
            } else {
                if (layers.feature.properties !== undefined) {
                    if (layers.feature.properties.area !== undefined)
                        myArea = myArea + parseFloat(layers.feature.properties.area.replace(/[^0-9]/g, ''))
                    if (layers.feature.properties.perimeter !== undefined)
                        myPerim = myPerim + parseFloat(layers.feature.properties.perimeter.replace(/[^0-9]/g, ''))
                    // myItems++;
                }
            }
            return {
                area: myArea,
                perim: myPerim,
                items: myItems
            }
        },
        async openv2format(savedData, clear = true) {
            let chblayer = false;
            let chblayertype = '';
            let chblayerimage = undefined;
            let chblayerdate = undefined;
            let chblayerprovider = undefined;
            let chbeagleviewlayerid = undefined;
            let chbeagleviewpurchaseid = undefined;
            let chbeagleviewtilematrixset = undefined;
            let chbeagleviewminzoom = undefined;
            let chbeagleviewmaxzoom = undefined;

            if (clear)
                this.clearScene(false);
            savedData = await this.cleanSavedData(savedData);

            if (typeof savedData[0] != 'undefined') {
                if (typeof savedData[0].metadata !== 'undefined') {
                    if (typeof savedData[0].metadata.baselayer !== 'undefined') {

                        chblayertype = savedData[0].metadata.baselayer;
                        chblayerimage = savedData[0].metadata.image;
                        chblayerdate = savedData[0].metadata.date;
                        chblayerprovider = savedData[0].metadata.provider;

                        // Extract EagleView specific metadata if present
                        if (savedData[0].metadata.provider === 'eagleview') {
                            chbeagleviewlayerid = savedData[0].metadata.eagleview_layer_id;
                            chbeagleviewpurchaseid = savedData[0].metadata.eagleview_purchase_id;
                            chbeagleviewtilematrixset = savedData[0].metadata.eagleview_tile_matrix_set;
                            chbeagleviewminzoom = savedData[0].metadata.eagleview_min_zoom;
                            chbeagleviewmaxzoom = savedData[0].metadata.eagleview_max_zoom;
                        }

                        chblayer = true

                    }
                }
            }


            // Disable any active leaflet-geoman drawing/editing modes before firing click event
            // This prevents TypeError when geoman handlers try to access undefined latlng coordinates
            if (map.pm) {
                // Disable any active drawing mode
                if (typeof map.pm.disableDraw === 'function') {
                    map.pm.disableDraw();
                }

                // Disable global edit mode if active
                if (typeof map.pm.disableGlobalEditMode === 'function') {
                    map.pm.disableGlobalEditMode();
                }

                // Iterate through layers and disable any individual editing modes
                map.eachLayer(function(layer) {
                    if (layer.pm && typeof layer.pm.enabled === 'function' && layer.pm.enabled() && typeof layer.pm.disable === 'function') {
                        layer.pm.disable();
                    }
                });
            }

            map.fireEvent('click');
            for (t = 0; t < savedData.length; t++) {
                let boo = L.geoJSON(savedData[t].features);
                let b = boo.getBounds();
                if (b.isValid() && savedData[t].metadata.vectorgroup) {
                    var area = turf.area(L.rectangle(b).toGeoJSON(15))
                    savedData[t].area = area
                } else {
                    savedData[t].area = 0;
                }
            }

            savedData.sort(function (a, b) {
                return parseFloat(b.area) - parseFloat(a.area);
            });
            for (var t = 0; t < savedData.length; t++) {
                savedData[t].index = (400 + t);
            }

            for (var z = 0; z < savedData.length; z++) {
                var parsedName = savedData[z].metadata.shortname;
                parsedName = parsedName.replace(/['"]+/g, '');
                parsedName = parsedName.replace(/\s+/g, '');
                parsedName = parsedName.replace(/["]/g, "-dq-");
                parsedName = parsedName.replace(/[']/g, "-sq-");
                parsedName = parsedName.replace(/[>]/g, "-gt-");
                parsedName = parsedName.replace(/[<]/g, "-lt-");
                parsedName = parsedName.replace(/[/]/g, "-bs-");
                parsedName = parsedName.replace(/[(]/g, "-bst-");
                parsedName = parsedName.replace(/[)]/g, "-bse-");
                parsedName = parsedName.replace(/[\\]/g, "-fs-");
                parsedName = parsedName.replace(/[&]/g, "-and-");
                parsedName = parsedName.replace(/[,]/g, "-comma-");
                parsedName = parsedName.replace(/[=]/g, "-equal-");
                parsedName = parsedName.replace(/[%]/g, "-percent-");
                parsedName = parsedName.replace(/[+]/g, "-plus-");
                if (this.presets[parsedName] === undefined) {
                    if (typeof savedData[z].metadata.category !== 'undefined')
                        if (savedData[z].metadata.category != null)
                            var category = savedData[z].metadata.category;
                        else
                            var category = "General";
                    else
                        var category = "General";
                    // Fix for import issues of beta versions of V2
                    if (category == "map" || category == "Map")
                        category = "General";

                    this.presets[parsedName] = {
                        "category": category,
                        "color": savedData[z].metadata.color,
                        "shortname": parsedName,
                        "name": savedData[z].metadata.name,
                        "stroke": true,
                        "fill": true,
                        "weight": savedData[z].metadata.weight,
                        "opacity": savedData[z].metadata.opacity,
                        "fillColor": savedData[z].metadata.fillColor,
                        "fillOpacity": savedData[z].metadata.fillOpacity,
                        "groupType": "Map",
                        "dashArray": savedData[z].metadata.dashArray,
                        "dashOffset": savedData[z].metadata.dashOffset,
                        "groupVisible": savedData[z].metadata.groupVisible,
                        "labelVisible": savedData[z].metadata.labelVisible,
                        "legendVisible": savedData[z].metadata.legendVisible,
                        "leafletlayer": L.featureGroup(),
                        /* "leafletlayer": L.featureGroup({
                             pane: parsedName
                         }) */
                    };
                    if (typeof savedData[z].metadata.vectorgroup !== 'undefined')
                        this.presets[parsedName].vectorgroup = true;
                    else if (typeof savedData[z].metadata.photogroup !== 'undefined')
                        this.presets[parsedName].photogroup = true;
                    else if (typeof savedData[z].metadata.markergroup !== 'undefined')
                        this.presets[parsedName].markergroup = true;
                    else
                        this.presets[parsedName].vectorgroup = true;
                    if (savedData[z].metadata.vectorgroup)
                        if (this.presets[parsedName].fillColor == null || this.presets[parsedName].fillColor == "") {
                            this.presets[parsedName].fillColor = this.presets[parsedName].color;
                        }
                    var feature = this.presets[parsedName].leafletlayer.feature = this.presets[parsedName].leafletlayer.feature ||
                        {};
                    feature.type = "FeatureGroup";
                    if (typeof savedData[z].metadata.vectorgroup !== 'undefined') {
                        if (typeof this.groups[category] == 'undefined') {
                            this.groups[category] = [];
                        }

                        var temp = {
                            name: savedData[z].metadata.name,
                            value: parsedName
                        };
                        this.groups[category].push(temp);

                    }

                    feature.properties = feature.properties || {};
                    this.presets[parsedName].leafletlayer.on('layeradd', this.updateGroups);
                    this.presets[parsedName].leafletlayer.on('layerremove', this.updateGroups);
                    this.copyMetadataToFeatureLayer(this.presets[parsedName].leafletlayer, parsedName);
                    drawnGroups.addLayer(this.presets[parsedName].leafletlayer);

                } else {
                    this.presets[parsedName].leafletlayer.metadata = savedData[z].metadata;
                    this.presets[parsedName]['legendVisible'] = typeof savedData[z].metadata['legendVisible'] == 'undefined' ? false : savedData[z].metadata['legendVisible'];
                    this.presets[parsedName]['groupVisible'] = typeof savedData[z].metadata['groupVisible'] == 'undefined' ? true : savedData[z].metadata['groupVisible'];
                    this.copyMetadataToFeatureLayer(this.presets[parsedName].leafletlayer, parsedName);
                }

                for (let j = 0; j < savedData[z].features.length; j++) {
                    if (savedData[z].features[j].properties !== undefined && savedData[z].features[j].properties !=
                        undefined) {
                        var featureProps = savedData[z].features[j].properties;
                        var featureGeo = savedData[z].features[j].geometry;
                    } else {
                        continue;
                    }


                    if (featureProps.type == 'Photo') {
                        this.addPhotoMarkers(featureProps.imageURL, featureProps.description, featureProps.date, "",
                            featureProps.markerURL,
                            featureProps.markerSize,
                            featureGeo.coordinates[1], featureGeo.coordinates[0], "", "", featureProps.buildname);
                        let imglist = store.get('map/imgList');
                        let imglistLength = imglist.length;
                        imglist[imglistLength] = [];
                        imglist[imglistLength][1] = featureProps.description;
                        imglist[imglistLength][2] = featureProps.date;
                        imglist[imglistLength][3] = featureProps.imageURL;
                        imglist[imglistLength][4] = featureProps.buildname;
                        imglist[imglistLength][5] = featureGeo.coordinates[1];
                        imglist[imglistLength][6] = featureGeo.coordinates[0];
                        continue;
                    }

                    if (featureProps.type == "Marker") {
                        if (!featureProps.markerURL)
                            featureProps.markerURL = myBaseURL + "/images/marker-orange.png";
                        if (!featureProps.markerSize)
                            featureProps.markerSize = [26, 41];

                        var marker = new L.Marker([featureGeo.coordinates[1], featureGeo.coordinates[0]], {
                            icon: new LeafletIcon({
                                iconUrl: featureProps.markerURL,
                                iconSize: featureProps.markerSize
                            })
                        });
                        marker.feature = marker.feature || {};
                        marker.feature.type = "Feature";
                        marker.feature.properties = marker.feature.properties || {};
                        marker.feature.properties = featureProps;
                        var markerlayer = marker;
                        this.presets[parsedName].leafletlayer.addLayer(markerlayer);
                        markerlayer.on('click', this.onPolyClick);
                        continue;
                    }
                    if (featureProps.type == "Text") {
                        this.addTextArea(featureProps.description, [featureGeo.coordinates[1], featureGeo.coordinates[0]],
                            featureProps.fontSize, featureProps);
                        continue;
                    }

                    if (featureProps.type == 'Circle') {
                        var circle = new L.Circle([featureGeo.coordinates[1], featureGeo.coordinates[0]], {
                            radius: featureProps.radius
                        });
                        circle.feature = circle.feature || {};
                        circle.feature.type = "Feature";
                        circle.feature.properties = circle.feature.properties || {};
                        circle.feature.properties = featureProps;
                        let mylayer2 = circle;

                        mylayer2.setStyle(this.presets[parsedName].leafletlayer.metadata);
                        this.presets[parsedName].leafletlayer.addLayer(mylayer2);
                        mylayer2.on('click', this.onPolyClick);
                        mylayer2.on('mouseover', function (e) {
                            this.setHighlightLayer(mylayer2);
                        }.bind(this));
                        mylayer2.on('mouseout', function (e) {
                            this.unsetHighlightLayer(mylayer2);
                        }.bind(this));
                        continue;
                    }

                    /* map.createPane('pane' + z + "_" + j);
                     map.getPane('pane' + z + "_" + j).style.zIndex = savedData[z].index;
                     var glayer = L.geoJSON(savedData[z].features[j], {
                         pane: 'pane' + z + "_" + j
                     });*/

                    var glayer = L.geoJSON(savedData[z].features[j]);
                    glayer.eachLayer(function (clayer) {
                        var feature = clayer.feature = clayer.feature || {};
                        feature.type = "Feature";
                        feature.properties = feature.properties || {};
                        feature.properties = savedData[z].features[j].properties;


                        if (feature.properties.type == "Line") {
                            var shapeOptions = _.omit(this.presets[parsedName].leafletlayer
                                .metadata,
                                ['fill', 'fillColor',
                                    'fillOpacity'
                                ]);
                            clayer.setStyle(shapeOptions);
                            if (clayer.feature.properties.anim == "true")
                                this.addPolylineAnimation(clayer, true);
                            if (clayer.feature.properties.arrow == "right")
                                this.addArrowhead(clayer, true);
                        } else {
                            try {
                                clayer.setStyle(this.presets[parsedName].leafletlayer.metadata);
                            } catch (ex) {
                                console.log(ex);
                                console.log(parsedName)
                                console.log(this.presets[parsedName].leafletlayer.metadata)
                                console.log(clayer)
                            }

                        }
                        this.presets[parsedName].leafletlayer.addLayer(clayer);
                        clayer.on('mouseover', function (e) {
                            this.setHighlightLayer(clayer);
                        }.bind(this));
                        clayer.on('mouseout', function (e) {
                            this.setHighlightLayer(clayer);
                        }.bind(this));
                        clayer.bringToBack();
                        clayer.on('click', this.onPolyClick);
                    }.bind(this));
                }

            }


            for (var preset in this.presets) {
                                if (!this.presets[preset].leafletlayer.metadata.groupVisible) {
                    drawnGroups.removeLayer(this.presets[preset].leafletlayer);
                }
            }


            setTimeout(function () {
                //sortLayers();
                this.syncPresets();

                let myzoomBounds = drawnGroups.getBounds();

                //convertSubtract2Poly();

                if (myzoomBounds.isValid()) {

                    map.fitBounds(myzoomBounds);
                    if (map.getBoundsZoom(myzoomBounds) < 18) {
                        myzoomBounds = drawnGroups.getBounds();
                        setTimeout(function () {
                            map.fitBounds(myzoomBounds);
                        }, 150
                        )
                        // map.setZoom(18);

                    }

                } else {
                    var maxBounds = L.latLngBounds(
                        L.latLng(24.0, -128.23), //Southwest
                        L.latLng(50.09, -59.14) //Northeast
                    );
                    map.fitBounds(maxBounds);
                    map.setZoom(4);
                }
                if (chblayer == true) {
                    const baselayers = store.get('baselayer/baseLayers');

                    let tLayer = baselayers.find(element => element.name == chblayertype);

                    if (chblayertype == "highres") {
                        if (typeof tLayer != 'undefined') {
                            store.set('baselayer/currentBaseLayer', tLayer.id)
                            store.set('baselayer/fileOpenTrigger', true)

                            // Set common metadata
                            if (chblayerdate) {
                                store.set('baselayer/tileDate', chblayerdate)
                            }
                            if (chblayerprovider) {
                                store.set('baselayer/tileProvider', chblayerprovider)
                            }

                            // Handle provider-specific metadata
                            if (chblayerprovider === 'eagleview') {
                                // For EagleView, store the layer metadata for token generation
                                if (chbeagleviewlayerid) {
                                    store.set('baselayer/eagleviewLayerId', chbeagleviewlayerid)
                                }
                                if (chbeagleviewtilematrixset) {
                                    store.set('baselayer/eagleviewTileMatrixSet', chbeagleviewtilematrixset)
                                }
                                if (chbeagleviewminzoom) {
                                    store.set('baselayer/eagleviewMinZoom', chbeagleviewminzoom)
                                }
                                if (chbeagleviewmaxzoom) {
                                    store.set('baselayer/eagleviewMaxZoom', chbeagleviewmaxzoom)
                                }
                                if (chbeagleviewpurchaseid) {
                                    store.set('baselayer/eagleviewPurchaseId', chbeagleviewpurchaseid)
                                }
                            } else if (chblayerprovider === 'nearmap' || !chblayerprovider) {
                                // For Nearmap or legacy files, use the tile URL
                                if (chblayerimage) {
                                    store.set('baselayer/tileUrl', chblayerimage)
                                }
                            }
                        }
                    } else {
                        if (typeof tLayer != 'undefined') {
                            store.set('baselayer/currentBaseLayer', tLayer.id)
                            store.set('baselayer/fileOpenTrigger', false)
                        }
                    }

                } else {
                    store.set('baselayer/currentBaseLayer', 2)
                }
            }.bind(this), 150);


        },
        groupBy(array, f) {
            var groups = {};
            array.forEach(function (o) {
                var group = JSON.stringify(f(o));
                groups[group] = groups[group] || [];
                groups[group].push(o);
            });
            return Object.keys(groups).map(function (group) {
                return groups[group];
            })
        },
        getWorkbook() {
            return XlsxPopulate.fromBlankAsync();
        },
        generate(exportData) {
            return this.getWorkbook()
                .then(function (workbook) {
                    var grandcolPerim = 0;
                    var grandcolArea = 0;
                    workbook.sheet(0).cell("A1").value("Group")
                    workbook.sheet(0).cell("B1").value("Name")
                    workbook.sheet(0).cell("C1").value("Area")
                    workbook.sheet(0).cell("D1").value("Unit")
                    workbook.sheet(0).cell("E1").value("Perimeter")
                    workbook.sheet(0).cell("F1").value("Unit")
                    workbook.sheet(0).cell("G1").value("Description")
                    let r = workbook.sheet(0).range("A1:G1");
                    r.style("fill", "OOOOOO");
                    r.style("fontColor", "FFFFFF");
                    workbook.sheet(0).column("A").width(25);
                    workbook.sheet(0).column("B").width(30);
                    workbook.sheet(0).column("C").width(20);
                    workbook.sheet(0).column("D").width(5);
                    workbook.sheet(0).column("E").width(20);
                    workbook.sheet(0).column("F").width(5);
                    workbook.sheet(0).column("G").width(40);
                    workbook.sheet(0).row(1).style("bold", true);
                    let sheetRow = 2
                    exportData.forEach(function (object) {
                        var sumcolPerim = 0;
                        var sumcolArea = 0;
                        var objGrp = "";

                        object.forEach(function (subobject) {
                            workbook.sheet(0).cell("A" + sheetRow).value(subobject.colGroup);
                            workbook.sheet(0).cell("B" + sheetRow).value(subobject.colName);
                            workbook.sheet(0).cell("C" + sheetRow).value(this.addCommas(
                                subobject.colArea));
                            workbook.sheet(0).cell("D" + sheetRow).value("sqft");
                            workbook.sheet(0).cell("E" + sheetRow).value(this.addCommas(
                                subobject.colPerim));
                            workbook.sheet(0).cell("F" + sheetRow).value("ft");
                            workbook.sheet(0).cell("G" + sheetRow).value(subobject.desc);
                            sumcolPerim += subobject.colPerim;
                            sumcolArea += subobject.colArea;
                            sheetRow++;
                            objGrp = subobject.colGroup;
                        }.bind(this));
                        workbook.sheet(0).cell("A" + sheetRow).value(objGrp + " Total");
                        workbook.sheet(0).cell("C" + sheetRow).value(this.addCommas(sumcolArea));
                        workbook.sheet(0).cell("D" + sheetRow).value("sqft");
                        workbook.sheet(0).cell("E" + sheetRow).value(this.addCommas(sumcolPerim));
                        workbook.sheet(0).cell("F" + sheetRow).value("ft");
                        workbook.sheet(0).row(sheetRow).style("bold", true);
                        r = workbook.sheet(0).range("A" + sheetRow + ":G" + sheetRow);
                        r.style("fill", "DCDCDC");
                        grandcolPerim += sumcolPerim;
                        grandcolArea += sumcolArea;
                        sheetRow++
                    }.bind(this));
                    workbook.sheet(0).cell("A" + sheetRow).value("Grand Total");
                    workbook.sheet(0).cell("C" + sheetRow).value(this.addCommas(grandcolArea));
                    workbook.sheet(0).cell("D" + sheetRow).value("sqft");
                    workbook.sheet(0).cell("E" + sheetRow).value(this.addCommas(grandcolPerim));
                    workbook.sheet(0).cell("F" + sheetRow).value("ft");
                    workbook.sheet(0).row(sheetRow).style("bold", true);
                    r = workbook.sheet(0).range("A" + sheetRow + ":G" + sheetRow);
                    r.style("fill", "696969");
                    r.style("fontColor", "FFFFFF");
                    return workbook.outputAsync();
                }.bind(this))
        },
        addCommas(intNum) {
            return (intNum + '').replace(/(\d)(?=(\d{3})+$)/g, '$1,');
        },
        generateBlob(exportData, fileName) {
            return this.generate(exportData)
                .then(function (blob) {
                    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
                        window.navigator.msSaveOrOpenBlob(blob, fileName +
                            ".xlsx");
                    } else {
                        var url = window.URL.createObjectURL(blob);
                        var a = document.createElement("a");
                        document.body.appendChild(a);
                        a.href = url;
                        a.download = fileName + ".xlsx";
                        a.click();
                        window.URL.revokeObjectURL(url);
                        document.body.removeChild(a);
                    }
                }.bind(this))
                .catch(function (err) {
                    console.log(err.message || err);
                    throw err;
                });
        },
        syncPresets() {
            for (var key in this.presets) {

                if (!this.presets.hasOwnProperty(key)) continue;
                else {
                    var obj = this.presets[key];
                    this.copyMetadataToFeatureLayer(this.presets[key].leafletlayer, key);
                }

            }
            setTimeout(function () {
                map.fire("updatelegend");
            }, 200);


        },
        addArrowhead(arrow, status) {
            if (status == true) {
                //CHECK TO SEE IF THERE IS ALREADY AN ANIMATION FOR THIS LINE
                var already_an_arrow = 0;
                for (let i = 0; i < this.arrowHeads.length; i++) {
                    if (this.arrowHeads[i][0] == arrow._leaflet_id) {
                        already_an_arrow = 1;
                    }
                }
                var existsindrawn = false;
                if (already_an_arrow == 0) {
                    var arrowHead = L.polylineDecorator(arrow, {
                        patterns: [{
                            offset: '100%',
                            repeat: 0,
                            symbol: L.Symbol.arrowHead({
                                pixelSize: 15,
                                polygon: false,
                                pathOptions: {
                                    color: this.presets[arrow.feature.properties.preset].color,
                                    stroke: true
                                }
                            })
                        }]
                    }).addTo(map);
                    arrow.feature.properties.arrow = 'right';
                    this.arrowHeads.push([arrow._leaflet_id, arrowHead._leaflet_id, arrowHead]);
                }
            } else {
                for (let i = 0; i < this.arrowHeads.length; i++) {
                    if (this.arrowHeads[i][0] == arrow._leaflet_id) {
                        map.removeLayer(this.arrowHeads[i][2]);
                        this.arrowHeads.splice(i, 1);
                    }
                }
                if (typeof arrow != 'undefined') {
                    if (typeof arrow.feature.properties.arrow != 'undefined')
                        arrow.feature.properties.arrow = 'none';
                }
            }
        },
        addPolylineAnimation(arrow, status) {
            if (status == true) {
                //CHECK TO SEE IF THERE IS ALREADY AN ANIMATION FOR THIS LINE
                var already_an_arrow = 0;
                for (let i = 0; i < this.animations.length; i++) {
                    if (this.animations[i][0] == arrow._leaflet_id) {
                        already_an_arrow = 1;
                    }
                }
                var existsindrawn = false;
                /*	drawnItems.eachLayer(function (layer) {
                    if (layer._leaflet_id == arrow._leaflet_id) {
                    existsindrawn = true;
                    }
                }); */
                if (already_an_arrow == 0) {
                    //THIS ADDS AN ANIMATED SNOWPLOW TO THE LINE
                    var arrowHead = L.polylineDecorator(arrow).addTo(layerGroup);
                    var matrix = {
                        1: 0.125,
                        2: 0.125,
                        3: 0.125,
                        4: 0.125,
                        5: 0.125,
                        6: 0.125,
                        7: 0.125,
                        8: 0.125,
                        9: 0.125,
                        10: 0.125,
                        11: 0.125,
                        12: 0.20,
                        13: 0.30,
                        14: 0.40,
                        15: 0.50,
                        16: 0.60,
                        17: 0.70,
                        18: 0.80,
                        19: 0.90,
                        20: 1,
                        21: 1.25,
                        22: 1.50
                    };
                    var arrowOffset = 0;
                    if (map) {
                        var anim = window.setInterval(function () {
                            if (!map) {
                                clearInterval(anim);
                                return;
                            }
                            let currentZoom = map.getZoom();
                            let iconx = 36 - ((20 - currentZoom) * 5);
                            let icony = 108 - ((20 - currentZoom) * 15);
                            let iconancx = 36 - ((20 - currentZoom) * 5);
                            let iconanxy = 46 - ((20 - currentZoom) * 5)
                            arrowHead.setPatterns([
                                {
                                    offset: arrowOffset + '%',
                                    repeat: 0,
                                    symbol: L.Symbol.marker({
                                        rotate: true,
                                        markerOptions: {
                                            icon: L.icon({
                                                zoom: false,
                                                iconUrl: arrow.feature.properties.AnimIcon,
                                                className: "allAnim",
                                                iconSize: [iconx, icony],
                                                iconAnchor: [iconancx, iconanxy]
                                            })
                                        }
                                    })
                                }
                            ])
                            if (++arrowOffset > 100)
                                arrowOffset = 0;
                        }, 120);
                        //Add the arrowhead to the arrowhead array
                        this.animations.push([arrow._leaflet_id, arrowHead._leaflet_id, arrowHead]);
                    }

                }
            } else {
                for (let i = 0; i < this.animations.length; i++) {
                    if (this.animations[i][0] == arrow._leaflet_id) {
                        map.removeLayer(this.animations[i][2]);
                        this.animations.splice(i, 1);
                    }
                }
            }
        },
        updateLayerInPanel(groupId, layer) {


            let groupdata = this.calcAreaPerimTotals(this.presets[groupId].leafletlayer);
            let grouparea = numeral(groupdata.area).format('0,0');
            grouparea = grouparea + ' sqft';
            let groupperim = numeral(_round(groupdata.perim, 2)).format('0,0');
            groupperim = groupperim + ' ft';
            this.presets[groupId].grouparea = grouparea;
            this.presets[groupId].groupperim = groupperim
            this.$forceUpdate()

        },

        updateGroups(e) {
            let layer = e.layer;
            let groupId = e.target.metadata.shortname;
            let type = e.type;
            let groupdata = this.calcAreaPerimTotals(this.presets[groupId].leafletlayer);
            map.fireEvent('autosave');
            if (type == 'layeradd') {


                let grouparea = numeral(groupdata.area).format('0,0');
                grouparea = grouparea + ' sqft';
                let groupperim = numeral(_round(groupdata.perim, 2)).format('0,0');
                groupperim = groupperim + ' ft';
                this.presets[groupId].grouparea = grouparea;
                this.presets[groupId].groupperim = groupperim


            } else {


                let grouparea = numeral(groupdata.area).format('0,0');
                grouparea = grouparea + ' sqft';
                let groupperim = numeral(_round(groupdata.perim, 2)).format('0,0');
                groupperim = groupperim + ' ft';
                this.presets[groupId].grouparea = grouparea;
                this.presets[groupId].groupperim = groupperim


            }
            this.$forceUpdate();
        },
        addPhotoMarkers(url, desc, date, tags = '', iconUrl, markerSize, lat1, lng1, lat2,
            lng2, buildname) {
            this.numBldgs = this.numBldgs + 1;
            let photoLinks = "";
            let photoInfo = '';
            let picURL = '';
            picURL = "<a href='" + url +
                "' target='_blank' ><img style='max-height:200px; max-width:300px;'   src='" +
                url + "' > </a>";
            var caption = desc;
            photoInfo += date + '<br/>';
            if (caption.length > 0) {
                photoInfo += '<br/><span contenteditable style="border: 1px dashed blue">' + caption +
                    '</span><br/>';
            }
            photoLinks = picURL + '<br/>';
            //to determine which icon to use, check the tags
            var markerUrl, markerSize;
            if (iconUrl.length > 0) {
                markerUrl = iconUrl;
                markerSize = markerSize;
            } else if (tags.length > 0) {
                if (tags.indexOf('pre') !== -1) {
                    markerUrl = LNumIconSquareRed.options.iconUrl;
                    markerSize = LNumIconSquareRed.options.iconSize;
                } else if (tags.indexOf('hazard') !== -1) {
                    markerUrl = LNumIconDiamondBlack.options.iconUrl;
                    markerSize = LNumIconDiamondBlack.options.iconSize;
                } else if (tags.indexOf('new') !== -1) {
                    markerUrl = LNumIconStarYellow.options.iconUrl;
                    markerSize = LNumIconStarYellow.options.iconSize;
                } else if (tags.indexOf('post') !== -1) {
                    markerUrl = LNumIconGreen.options.iconUrl;
                    markerSize = LNumIconGreen.options.iconSize;
                }
            } else {
                markerUrl = LNumIconBlue.options.iconUrl;
                markerSize = LNumIconBlue.options.iconSize;
            }
            let lat, lng;
            if ((lat2 == '') || (lat2 == '0') || (lat2 == 0) || (lat2 == null)) {
                lat = lat1;
                lng = lng1;
            } else {
                lat = lat2;
                lng = lng2;
            }

            //var marker = new L.Marker([imglist[i][5], imglist[i][6]], {
            let marker = new L.Marker([lat, lng], {
                id: this.numBldgs,
                icon: new L.NumberedDivIcon({
                    iconUrl: markerUrl,
                    iconSize: markerSize,
                    number: this.numBldgs
                })
                //icon:	new L.NumberedDivIcon({iconUrl: LNumIconDiamondBlack.options.iconUrl, iconSize: LNumIconDiamondBlack.options.iconSize, number: numBldgs})
            })
            // Create an element to hold all your text and markup

            let layer = marker;
            let feature = layer.feature = layer.feature || {};
            feature.type = "Feature";
            feature.properties = feature.properties || {};
            if (layer instanceof L.Marker) {
                feature.properties["type"] = 'Photo';
                feature.properties["location"] = layer._latlng;
                feature.properties["imageURL"] = url;
                feature.properties["markerURL"] = markerUrl;
                feature.properties["markerSize"] = markerSize;
                feature.properties["number"] = this.numBldgs;
                feature.properties["description"] = desc;
                feature.properties["date"] = date;
                feature.properties["buildname"] = buildname;
            }

            marker.on('click', this.onPolyClick);
            marker.on('dragend', function (event) {
                var marker = event.target;
                var position = marker.getLatLng();
                //event.target.setIcon(LwarningIcon);
                marker.setLatLng(position, {
                    id: this.numBldgs,
                    draggable: 'true'
                });
            });
            marker.on('popupopen', function () {
                var marker = this;
                $(".marker-colors").on("click", function () {
                    /*   marker.setIcon(eval($(this).data("id")));*/
                    marker.setIcon($(this)[0].dataset.id);
                    //marker.setIcon(LwarningIcon);
                })
            });


            let obj = photoURL2GroupName(marker.feature.properties.markerURL);
            if (obj) {
                let preset = this.addPhotoPreset(obj.shortname, obj.name);
                preset.leafletlayer.addLayer(marker);
            } else
                this.presets["photos"].leafletlayer.addLayer(layer);


        },
        toggleGroup(groupId) {

            if (this.presets[groupId].leafletlayer.metadata.groupVisible) {
                this.presets[groupId].leafletlayer.metadata.groupVisible = false;
                this.presets[groupId].groupVisible = false;
                drawnGroups.removeLayer(this.presets[groupId].leafletlayer);

                if (this.globalSelectedGroup == groupId)
                    this.selectGroup("none");

            } else {
                this.presets[groupId].leafletlayer.metadata.groupVisible = true;
                this.presets[groupId].groupVisible = true;
                drawnGroups.addLayer(this.presets[groupId].leafletlayer);
                this.selectGroup(groupId);
                window.fitText(document.getElementsByClassName('allTextarea'));
            }
            this.$forceUpdate();
        },

        addMarkerPreset(shortname, name) {
            if (this.presets.hasOwnProperty(shortname))
                return this.presets[shortname];
            else {
                this.presets[shortname] = {};
                this.presets[shortname].name = name;
                this.presets[shortname].shortname = shortname;
                this.presets[shortname].groupVisible = true;
                this.presets[shortname].markergroup = true;
                this.presets[shortname].groupType = "Map";
                this.presets[shortname].leafletlayer = L.featureGroup();
                this.presets[shortname].leafletlayer.on('layeradd', this.updateGroups);
                this.presets[shortname].leafletlayer.on('layerremove', this.updateGroups);
                this.copyMetadataToFeatureLayer(this.presets[shortname].leafletlayer, shortname);
                drawnGroups.addLayer(this.presets[shortname].leafletlayer);
                return this.presets[shortname];
            }
        },
        addPhotoPreset(shortname, name) {
            if (this.presets.hasOwnProperty(shortname))
                return this.presets[shortname];
            else {
                this.presets[shortname] = {};
                this.presets[shortname].name = name;
                this.presets[shortname].shortname = shortname;
                this.presets[shortname].groupVisible = true;
                this.presets[shortname].photogroup = true;
                this.presets[shortname].groupType = "Map"
                this.presets[shortname].leafletlayer = L.featureGroup();
                this.presets[shortname].leafletlayer.on('layeradd', this.updateGroups);
                this.presets[shortname].leafletlayer.on('layerremove', this.updateGroups);
                this.copyMetadataToFeatureLayer(this.presets[shortname].leafletlayer, shortname);
                drawnGroups.addLayer(this.presets[shortname].leafletlayer);
                return this.presets[shortname];
            }
        },
        addTextArea(text = 'Click to Edit Text<br>Drag to Move Text', location = map.getCenter(),
            fontSize = 9, layerproperties) {
            //TEXT LABELS
            var htmlp = text;
            let tempText = text;
            tempText = tempText?.replace(/(?:\r\n|\r|\n)/g, '<br>').replace(/ /g, '&nbsp;');
            if (typeof layerproperties != 'undefined') {
                if (typeof layerproperties.desctype !== "undefined") {
                    if (layerproperties.desctype == 'none')
                        htmlp = tempText;
                    else {
                        var obj = JSON.parse(layerproperties.description);

                        let temphtml = "<table class='textgrid'>"

                        for (var k in obj) {
                            if (obj.hasOwnProperty(k)) {
                                temphtml += '<tr><td>' + k + '</td><td>' + obj[k] +
                                    '</td></tr>';
                            }
                        }
                        temphtml += "</table>";
                        htmlp = temphtml;

                    }
                } else {
                    htmlp = tempText;
                }
            }


            var k = [(fontSize / 2) * 50 * matrix[18], (fontSize / 2) * 20 * matrix[18]];
            var label = new L.Marker(location, {
                icon: new CustomDivIcon({
                    className: 'allTextarea',
                    html: htmlp,
                    iconSize: k,
                    iconAnchor: [0, 0],
                    span: true,
                    textColor: layerproperties?.textColor ?? '#FFFFFF',
                    background: layerproperties?.background ?? false,
                    spanClassName: 'allTextAreaSpan',
                }),
                draggable: 'true'
            });

            var layer = label;
            var feature = layer.feature = layer.feature || {};
            feature.type = "Feature";
            feature.properties = feature.properties || {};
            layer.feature.properties.myId = 'This is myId';
            layer.feature.properties.type = 'Text';
            layer.feature.properties.fontSize = fontSize;
            feature.properties["textColor"] = layerproperties?.textColor ?? '#FFFFFF';
            feature.properties["background"] = layerproperties?.background ?? false;
            if (typeof layerproperties.desctype !== "undefined")
                layer.feature.properties.desctype = layerproperties.desctype
            if (typeof layerproperties.descrows !== "undefined")
                layer.feature.properties.descrows = layerproperties.descrows
            layer.feature.properties.description = text;
            this.shapeCounter += 1;
            layer.feature.properties.id = this.shapeCounter;
            layer.feature.properties.name = "Text-" + this.shapeCounter;

            this.presets['textinternal'].leafletlayer.addLayer(layer);
            layer.on('click', this.onPolyClick);
            //END TEXT LABELS

            window.fitText(document.getElementsByClassName('allTextarea'));
        }
    },
    computed: {
        //TODO: Nasir please check this
        globalMapManagerComputed() {
            return window.globalMapManager
        },
        globalBaseURLComputed() {
            return window.myBaseURL
        },
        cName() {
            return mycompanyname;
        },
        saveText() {
            if (this.$store.get('estimator/currentEstimateId')) {
                return 'Save and Return';
            }
            return 'Save';
        },
        fileNameParsed() {
            return store.get('map/fileName').slice(0, 32);
        },
        fileName: VuexPathify.sync('map/fileName'),
        ...VuexPathify.sync('groups', [
            'groups',
            'globalSelectedGroup',
        ]),
        filteredFiles() {
            var k = this.files;
            if (this.filter.sites.length > 0) {
                k = _.filter(k, function (item) {
                    return this.filter.sites.includes(item.sitename)

                }.bind(this))
            }

            return k;
        },
        uniqueSites() {

            return _.uniqBy(this.files, 'sitename').sort(function (a, b) {
                return a.sitename.localeCompare(b.sitename)
            });
        },
        buildingID: VuexPathify.sync('map/buildingID'),
        saveAsRequired: VuexPathify.sync('map/saveAsRequired'),
        numBldgs: VuexPathify.sync('map/numBldgs'),
        shapeCounter: VuexPathify.sync('map/shapeCounter'),
        mapState: VuexPathify.sync('map/mapState'),
        mergemapDialog: VuexPathify.sync('map/mergemapDialog'),
        shareMapDialog: VuexPathify.sync('map/shareMapDialog'),
        urlKey: VuexPathify.sync('map/urlKey'),
        groupItems() {
            let items = [];
            for (let category in this.groups) {
                let item = {
                    header: category
                };
                items.push(item);
                let value2 = this.groups[category];


                for (let j = 0; j < value2.length; j++) {
                    let item = {}
                    item.value = value2[j].value
                    item.text = value2[j].name
                    items.push(item);
                }
            }
            return items;
        }
    }
}
